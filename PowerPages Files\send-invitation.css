.send-invitation-container {
    max-width: 600px;
}

.send-invitation-container h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 2px;
    background-color: var(--primary-red);
    margin: 1rem auto;
}

#sendButton {
    background-color: var(--primary-red);
    color: var(--color-white);
    border: 2px solid var(--primary-red);
}

#sendButton:hover {
    background-color: var(--primary-red-dark);
    border-color: var(--primary-red-dark);
}

.border-top {
    border-top: 1px solid var(--gray-border) !important;
    padding-top: 1.5rem !important;
    margin-top: 1.5rem !important;
}

#recentInvitations {
    font-size: 0.875rem;
    color: var(--gray-medium);
    line-height: 1.5;
    padding: 1rem;
    background-color: var(--gray-light);
    border-radius: var(--radius-md);
    border-left: 3px solid var(--primary-red);
}

.recent-invitation-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: var(--gray-light);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-red);
}

.recent-invitation-item .invitation-status.sent {
    background-color: #dbeafe;
    color: #1e40af;
}

.recent-invitation-item .invitation-status.error {
    background-color: var(--primary-red-light);
    color: var(--primary-red);
}

.col-md-10 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.row.justify-content-center {
    margin-left: 0;
    margin-right: 0;
}

#sendButton.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: var(--color-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification System Integration */
.send-invitation-container .notification-container {
    margin-bottom: var(--spacing-md);
}

.send-invitation-container .notification {
    margin-bottom: var(--spacing-sm);
}

/* Enhanced styling for invitation-specific notifications */
.send-invitation-container .notification-success {
    border-left-color: var(--primary-red);
    background-color: #f0f9ff;
}

.send-invitation-container .notification-success .notification-icon {
    color: var(--primary-red);
}

.send-invitation-container .notification-error {
    border-left-width: 5px;
}

/* Rate limiting and API error styling */
.send-invitation-container .notification-warning {
    border-left-color: #f59e0b;
    background-color: #fef3c7;
}

/* Ensure notifications don't interfere with form layout */
.send-invitation-container #invitationForm {
    margin-top: var(--spacing-sm);
}

/* Legacy notification compatibility */
.send-invitation-container #errorMessage,
.send-invitation-container #successMessage {
    margin-bottom: var(--spacing-md);
}

/* Loading state integration */
.send-invitation-container .notification-loading {
    background-color: var(--gray-light);
    border-left-color: var(--primary-red);
}

@media (max-width: 768px) {
    .send-invitation-container .notification {
        padding: var(--spacing-sm);
        font-size: 0.875rem;
    }

    .send-invitation-container .notification-title {
        font-size: 0.85rem;
    }

    .send-invitation-container .notification-message {
        font-size: 0.8rem;
    }
}
