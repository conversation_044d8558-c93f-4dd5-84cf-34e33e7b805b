using Microsoft.Extensions.Configuration;

namespace PasswordHistoryValidator.Shared;


public static class ConfigurationValidator
{
    private static readonly string[] RequiredConfigurationKeys =
    {

        "SendGrid:ApiKey",
        "SendGrid:FromEmail",


        "EntraExternalID:TenantId",
        "EntraExternalID:ClientId",
        "EntraExternalID:ClientSecret",
        "EntraExternalID:DefaultDomain",


        "PasswordReset:BaseUrl",
        "AccountRegistration:BaseUrl",
        "ApplicationName"
    };


    public static void ValidateRequiredConfiguration(IConfiguration configuration)
    {
        var missingKeys = new List<string>();


        foreach (var key in RequiredConfigurationKeys)
        {
            if (string.IsNullOrEmpty(configuration[key]))
            {
                missingKeys.Add(key);
            }
        }


        var azureWebJobsStorage = configuration["AzureWebJobsStorage"];
        var storageConnectionString = configuration["Storage:ConnectionString"];
        if (string.IsNullOrEmpty(azureWebJobsStorage) && string.IsNullOrEmpty(storageConnectionString))
        {
            missingKeys.Add("AzureWebJobsStorage OR Storage:ConnectionString");
        }

        // Simple Azure environment detection - enforce Key Vault for sensitive values
        var isAzure = !string.IsNullOrEmpty(configuration["WEBSITE_SITE_NAME"]);
        if (isAzure)
        {
            var clientSecret = configuration["EntraExternalID:ClientSecret"];
            if (!string.IsNullOrEmpty(clientSecret) && !clientSecret.Contains("@Microsoft.KeyVault"))
            {
                missingKeys.Add("EntraExternalID:ClientSecret must use Key Vault reference in Azure (@Microsoft.KeyVault)");
            }

            var sendGridKey = configuration["SendGrid:ApiKey"];
            if (!string.IsNullOrEmpty(sendGridKey) && !sendGridKey.Contains("@Microsoft.KeyVault"))
            {
                missingKeys.Add("SendGrid:ApiKey must use Key Vault reference in Azure (@Microsoft.KeyVault)");
            }
        }


        if (missingKeys.Count > 0)
        {
            var errorMessage = "Required configuration values are missing:\n\n" +
                              string.Join("\n", missingKeys.Select(k => $"• {k}")) +
                              "\n\nPlease configure these values in local.settings.json or Azure App Settings.";

            throw new InvalidOperationException(errorMessage);
        }
    }
}