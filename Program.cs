using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Azure.Storage.Blobs;
using Azure.Identity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Graph;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using System.Text.Json;
using Microsoft.Azure.Functions.Worker;
using DotNetEnv;


// Load .env file for local development
Env.Load();

var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureAppConfiguration((context, builder) =>
    {
        builder.AddEnvironmentVariables()
               .AddUserSecrets<Program>();

        var settings = builder.Build();

        var keyVaultUrl = settings.GetValue<string>("KeyVaultUrl");
        if (!string.IsNullOrEmpty(keyVaultUrl))
        {
            try
            {
                builder.AddAzureKeyVault(
                    new Uri(keyVaultUrl),
                    new DefaultAzureCredential(
                        new DefaultAzureCredentialOptions
                        {
                            ManagedIdentityClientId = settings.GetValue<string>("UserAssignedClientId")
                        }));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"WARNING: Failed to configure Key Vault: {ex.Message}. Continuing without Key Vault integration.");
            }
        }
    })
    .ConfigureServices((context, services) =>
    {
        var configuration = context.Configuration;

        services.AddApplicationInsightsTelemetryWorkerService();
        services.ConfigureFunctionsApplicationInsights();

        services.AddMemoryCache();

        ConfigurationValidator.ValidateRequiredConfiguration(configuration);
        services.Configure<SendGridOptions>(configuration.GetSection(SendGridOptions.SectionName));
        services.Configure<EntraExternalIDOptions>(configuration.GetSection(EntraExternalIDOptions.SectionName));
        services.Configure<PasswordHistoryOptions>(configuration.GetSection(PasswordHistoryOptions.SectionName));
        services.Configure<RateLimitOptions>(configuration.GetSection(RateLimitOptions.SectionName));
        services.Configure<PasswordResetOptions>(configuration.GetSection(PasswordResetOptions.SectionName));
        services.Configure<AccountRegistrationOptions>(configuration.GetSection(AccountRegistrationOptions.SectionName));
        services.Configure<InvitationOptions>(configuration.GetSection(InvitationOptions.SectionName));


        services.Configure<ApplicationOptions>(options =>
        {
            options.ApplicationName = configuration["ApplicationName"] ?? string.Empty;
            options.KeyVaultUrl = configuration["KeyVaultUrl"] ?? string.Empty;
            options.PasswordExpirationDays = configuration.GetValue<int>("PASSWORD_EXPIRATION_DAYS", 90);
            options.PasswordWarningDays = configuration.GetValue<int>("PASSWORD_WARNING_DAYS", 15);
            options.InvitationTokenExpirationDays = configuration.GetValue<int>("INVITATION_TOKEN_EXPIRATION_DAYS", 45);
            options.AccountRetentionDays = configuration.GetValue<int>("ACCOUNT_RETENTION_DAYS", 90);
        });

        services.Configure<StorageOptions>(options =>
        {
            options.ConnectionString = configuration["AzureWebJobsStorage"] ?? configuration["Storage:ConnectionString"] ?? string.Empty;
        });

        services.AddSingleton<JsonSerializerOptions>(provider => new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true,
            WriteIndented = false
        });


        services.AddHttpClient("default")
                .AddStandardResilienceHandler(options =>
                {
                    options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(30);
                    options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(15);
                });


        services.AddSingleton<BlobServiceClient>(serviceProvider =>
        {
            var storageOptions = serviceProvider.GetRequiredService<IOptions<StorageOptions>>();
            var connectionString = storageOptions.Value.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Storage connection string is required but not configured. Configure AzureWebJobsStorage or Storage:ConnectionString.");
            }

            return new BlobServiceClient(connectionString);
        });


        services.AddScoped<GraphServiceClient>(serviceProvider =>
        {
            var entraOptions = serviceProvider.GetRequiredService<IOptions<EntraExternalIDOptions>>();
            var options = entraOptions.Value;

            if (string.IsNullOrEmpty(options.ClientId) || string.IsNullOrEmpty(options.ClientSecret) || string.IsNullOrEmpty(options.TenantId))
            {
                throw new InvalidOperationException("Entra External ID configuration is incomplete. Configure EntraExternalID:ClientId, ClientSecret, and TenantId.");
            }

            var credential = new ClientSecretCredential(options.TenantId, options.ClientId, options.ClientSecret);
            return new GraphServiceClient(credential);
        });

        services.AddSingleton<EmailService>(serviceProvider =>
        {
            var logger = serviceProvider.GetRequiredService<ILogger<EmailService>>();
            var sendGridOptions = serviceProvider.GetRequiredService<IOptions<SendGridOptions>>();
            var appOptions = serviceProvider.GetRequiredService<IOptions<ApplicationOptions>>();
            var passwordResetOptions = serviceProvider.GetRequiredService<IOptions<PasswordResetOptions>>();
            var accountRegistrationOptions = serviceProvider.GetRequiredService<IOptions<AccountRegistrationOptions>>();

            return new EmailService(logger, sendGridOptions, appOptions, passwordResetOptions, accountRegistrationOptions);
        });

        services.AddScoped<PasswordHistoryService>();
        services.AddSingleton<RateLimitService>();
        services.AddSingleton<ResetTokenManager>();
        services.AddSingleton<InvitationTokenManager>();


        services.AddScoped<PasswordHistoryValidator.PasswordFunction>();
        services.AddScoped<PasswordHistoryValidator.UtilityFunction>();
        services.AddScoped<PasswordHistoryValidator.InvitationFunction>();
        services.AddScoped<PasswordHistoryValidator.RegistrationFunction>();
    })
    .Build();

host.Run();
