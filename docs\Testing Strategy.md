# PowerPagesCustomAuth Testing Strategy

## Overview

This document provides a comprehensive testing strategy for the PowerPagesCustomAuth application, covering all Azure Functions and Power Pages integration points. The strategy emphasizes practical, straightforward testing approaches suitable for the current architecture.

## Application Components

### Azure Functions
- **PasswordService**: Password operations (validate, reset, change)
- **RegistrationService**: User account creation
- **UtilityService**: Health checks, cleanup, notifications
- **InvitationService**: User invitation management

### Power Pages Integration
- Registration page with invitation token validation
- Forgot password flow
- Password reset completion
- Invitation management (admin)

---

## 1. Authentication Testing

### Current Implementation
Authentication is handled entirely by **Entra External ID** through Power Pages native authentication. No custom authentication endpoints are used.

### Manual Testing (Power Pages)

**Login Flow:**
1. Navigate to Power Pages home page
2. Click "Sign In" link
3. Redirected to Entra External ID login page
4. Enter valid email/password for existing user
5. Verify successful redirect to authenticated area

**Error Scenarios:**
- Invalid email format
- Non-existent user
- Wrong password
- Account locked/disabled

**Note:** Authentication testing focuses on the Entra External ID integration rather than custom API endpoints.

---

## 2. Password Service Testing

### Operations
- `validate` - Password strength and history validation
- `update-history` - Update password history after change
- `reset-initiate` - Start password reset flow
- `reset-complete` - Complete password reset with token

### Manual Testing (Power Pages)

**Forgot Password Flow:**
1. Navigate to forgot password page
2. Enter valid email address
3. Submit form
4. Check email for reset link
5. Click reset link
6. Enter new password
7. Verify password meets requirements
8. Complete reset process

**Password Change Flow:**
1. Login to authenticated area
2. Navigate to password change page
3. Enter current password
4. Enter new password (test history validation)
5. Confirm password change

### API Testing (Direct)

**Password Reset Initiate:**
```bash
curl -X POST "https://your-function-app.azurewebsites.net/api/PasswordService?operation=reset-initiate&code=FUNCTION_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "applicationName": "TestApp"
  }'
```

**Password Reset Complete:**
```bash
curl -X POST "https://your-function-app.azurewebsites.net/api/PasswordService?operation=reset-complete&code=FUNCTION_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "reset-token-from-email",
    "verificationCode": "123456",
    "newPassword": "NewPassword123!",
    "applicationName": "TestApp"
  }'
```

**Test Cases:**
- Valid password → 200 OK
- Password in history → 409 Conflict
- Weak password → 400 validation error
- Invalid token → 400 Bad Request
- Expired token → 400 Bad Request

---

## 3. Registration Service Testing

### Operations
- `register` - Create new user account with invitation validation

### Manual Testing (Power Pages)

**Registration Flow:**
1. Receive invitation email with token
2. Click registration link
3. Fill out registration form
4. Enter invitation code from email
5. Create strong password
6. Submit registration
7. Verify account creation
8. Test login with new credentials

### API Testing (Direct)

**User Registration:**
```bash
curl -X POST "https://your-function-app.azurewebsites.net/api/RegistrationService?operation=register&code=FUNCTION_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "NewPassword123!",
    "firstName": "John",
    "lastName": "Doe",
    "applicationName": "TestApp",
    "invitationCode": "123456"
  }'
```

**Test Cases:**
- Valid registration → 200 OK with user creation
- Invalid invitation code → 400 Bad Request
- Expired invitation → 400 Bad Request
- Duplicate email → 400 Bad Request
- Weak password → 400 validation error

---

## 4. Utility Service Testing

### Operations
- `health` - System health check
- `cleanup-tokens` - Remove expired tokens
- `stats` - System statistics
- `notify-expiring-passwords` - Send expiration notifications

### API Testing (Direct)

**Health Check:**
```bash
curl -X POST "https://your-function-app.azurewebsites.net/api/UtilityService?operation=health&code=FUNCTION_KEY"
```

**Expected Response:**
```json
{
  "data": {
    "status": "healthy",
    "services": {
      "blobStorage": "healthy",
      "graphApi": "healthy",
      "emailService": "healthy"
    }
  }
}
```

**Cleanup Tokens:**
```bash
curl -X POST "https://your-function-app.azurewebsites.net/api/UtilityService?operation=cleanup-tokens&code=FUNCTION_KEY"
```

**Test Cases:**
- All services healthy → 200 OK
- Service unavailable → 200 with degraded status
- Cleanup successful → 200 with count of removed tokens

---

## 5. Integration Testing Scenarios

### End-to-End User Flows

**Complete Registration Flow:**
1. Admin sends invitation
2. User receives invitation email
3. User clicks registration link
4. User completes registration
5. User logs in successfully
6. User changes password
7. User logs in with new password

**Password Reset Flow:**
1. User forgets password
2. User requests password reset
3. User receives reset email
4. User completes password reset
5. User logs in with new password

### Cross-Function Dependencies

**Password History Validation:**
1. Register new user
2. Change password multiple times
3. Attempt to reuse old password
4. Verify rejection with appropriate error

**Token Management:**
1. Generate invitation token
2. Validate token before expiration
3. Use token for registration
4. Verify token marked as used
5. Attempt to reuse token (should fail)

---

## 6. Error Handling Testing

### Common Error Scenarios

**Network Issues:**
- Function app unavailable
- Blob storage connection failure
- Graph API timeout
- Email service failure

**Data Issues:**
- Malformed JSON requests
- Missing required fields
- Invalid data types
- SQL injection attempts
- XSS attempts

**Business Logic Issues:**
- Expired tokens
- Rate limiting
- Duplicate operations
- Invalid state transitions

### Testing Approach

**Manual Error Testing:**
1. Disconnect network during operations
2. Enter invalid data in forms
3. Manipulate URLs and tokens
4. Test with expired sessions

**API Error Testing:**
```bash
# Test with invalid JSON
curl -X POST "url" -d 'invalid-json'

# Test with missing fields
curl -X POST "url" -d '{}'

# Test with malicious input
curl -X POST "url" -d '{"email":"<script>alert(1)</script>"}'
```

---

## 7. Performance Testing

### Load Testing Scenarios

**Authentication Load:**
- 100 concurrent login attempts
- Monitor response times
- Check for rate limiting

**Registration Load:**
- 50 concurrent registrations
- Monitor blob storage performance
- Check Graph API limits

### Simple Load Testing

**Using curl and bash:**
```bash
# Simple concurrent test
for i in {1..10}; do
  curl -X POST "url" -d "data" &
done
wait
```

**Monitor:**
- Response times
- Error rates
- Function app metrics
- Storage account metrics

---

## 8. Security Testing

### Authentication Security

**Test Cases:**
- Function key validation
- CORS configuration
- Input sanitization
- SQL injection prevention
- XSS prevention

### Data Security

**Test Cases:**
- Password hashing verification
- Sensitive data in logs
- Token encryption
- Secure transmission (HTTPS)

### Simple Security Tests

**Input Validation:**
```bash
# Test XSS
curl -X POST "url" -d '{"email":"<script>alert(1)</script>"}'

# Test SQL injection
curl -X POST "url" -d '{"email":"<EMAIL>; DROP TABLE users;"}'

# Test oversized input
curl -X POST "url" -d '{"email":"'$(python -c "print('a'*10000)")'"}'
```

---

## 9. Monitoring and Logging

### Key Metrics to Monitor

**Function Performance:**
- Execution duration
- Success/failure rates
- Memory usage
- Cold start frequency

**Business Metrics:**
- Registration success rate
- Password reset completion rate
- Login success rate
- Error distribution

### Log Analysis

**Important Log Entries:**
- Authentication failures
- Password policy violations
- Token validation failures
- Service connectivity issues

**Correlation ID Tracking:**
- Track requests across functions
- Identify error patterns
- Debug user-specific issues

---

## 10. Testing Tools and Setup

### Recommended Tools

**API Testing:**
- Postman (collection of requests)
- curl (command line testing)
- Azure Functions Core Tools (local testing)

**Browser Testing:**
- Chrome DevTools (network monitoring)
- Browser developer tools
- Incognito mode (session testing)

**Monitoring:**
- Azure Application Insights
- Azure Function App logs
- Storage account metrics

### Test Data Setup

**Test Users:**
- Create test users in Entra External ID
- Generate test invitation tokens
- Set up test email addresses
- Configure test application contexts

**Test Environment:**
- Separate test Function App
- Test storage account
- Test SendGrid configuration
- Test Power Pages site

---

## 11. Automated Testing Recommendations

### Unit Testing

**Focus Areas:**
- Password validation logic
- Token generation/validation
- Input sanitization
- Business rule enforcement

### Integration Testing

**Key Scenarios:**
- Function-to-function communication
- External service integration
- End-to-end user flows
- Error handling paths

### Simple Automation

**PowerShell Scripts:**
```powershell
# Test health endpoint
$response = Invoke-RestMethod -Uri "health-url" -Method POST
if ($response.data.status -eq "healthy") {
    Write-Host "Health check passed"
} else {
    Write-Host "Health check failed"
    exit 1
}
```

**Bash Scripts:**
```bash
#!/bin/bash
# Simple API test script
response=$(curl -s -X POST "url" -d "data")
if echo "$response" | grep -q "success"; then
    echo "Test passed"
else
    echo "Test failed: $response"
    exit 1
fi
```

---

## 12. Testing Checklist

### Pre-Deployment Testing

- [ ] All API endpoints respond correctly
- [ ] Power Pages forms submit successfully
- [ ] Email notifications are sent
- [ ] Password history is enforced
- [ ] Token validation works
- [ ] Error handling is appropriate
- [ ] CORS is configured correctly
- [ ] Function keys are working

### Post-Deployment Testing

- [ ] Health checks pass
- [ ] End-to-end user flows work
- [ ] Monitoring is capturing data
- [ ] Logs are being generated
- [ ] Performance is acceptable
- [ ] Security measures are active

### Regular Testing

- [ ] Weekly health checks
- [ ] Monthly end-to-end testing
- [ ] Quarterly security review
- [ ] Annual load testing

---

This testing strategy provides a comprehensive yet practical approach to validating the PowerPagesCustomAuth application. Focus on the manual testing scenarios first, then implement API testing, and finally consider automation for critical paths.
