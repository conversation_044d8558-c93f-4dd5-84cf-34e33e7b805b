using System.ComponentModel.DataAnnotations;

namespace PasswordHistoryValidator.Models;


public class PasswordValidationRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string Password { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;
    
    public string? UserId { get; set; }
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}


public class PasswordResetRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}


public class PasswordResetCompleteRequest
{
    [Required(ErrorMessage = "Token is required")]
    public string Token { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Verification code is required")]
    public string VerificationCode { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "New password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string NewPassword { get; set; } = string.Empty;
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}




public class TokenValidationRequest
{
    [Required(ErrorMessage = "Token is required")]
    public string Token { get; set; } = string.Empty;
    
    public string? VerificationCode { get; set; }
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}
