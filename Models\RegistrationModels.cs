using System.ComponentModel.DataAnnotations;

namespace PasswordHistoryValidator.Models;


public class UserRegistrationRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string Password { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "First name is required")]
    public string FirstName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Last name is required")]
    public string LastName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}


public class InvitationValidationRequest
{
    [Required(ErrorMessage = "Token is required")]
    public string Token { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Verification code is required")]
    public string VerificationCode { get; set; } = string.Empty;
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}
