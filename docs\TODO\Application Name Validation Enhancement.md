# Application Name Validation Enhancement

**Priority:** Lower Priority (requires management discussion)  
**Category:** Security Enhancement  
**Impact:** Medium - Improves application isolation and prevents configuration errors  
**Effort:** Low - Straightforward validation logic implementation

---

## Current Implementation

**Basic Validation in Place:**
- Application names are validated for presence (not null/empty)
- Used in Entra External ID Department field for user isolation
- Included in all token operations for application context
- Validated during user registration and password operations

**Current Validation Logic:**
```csharp
// Basic null/empty check
if (string.IsNullOrEmpty(applicationName))
{
    return await HttpResponseHelper.CreateErrorResponse(req, "ApplicationName is required", correlationId, _jsonOptions);
}
```

---

## Enhancement Opportunities

### 1. **Enhanced Validation Rules**

**Proposed Validation Criteria:**
- **Length**: 3-50 characters
- **Characters**: Alphanumeric, spaces, hyphens, underscores only
- **Format**: No leading/trailing spaces
- **Reserved Names**: Prevent system reserved names
- **Uniqueness**: Optional - validate against known application registry

**Implementation Example:**
```csharp
public static class ApplicationNameValidator
{
    private static readonly string[] ReservedNames = { "admin", "system", "test", "default", "api" };
    private static readonly Regex ValidFormat = new Regex(@"^[a-zA-Z0-9\s\-_]{3,50}$");
    
    public static (bool IsValid, string ErrorMessage) ValidateApplicationName(string applicationName)
    {
        if (string.IsNullOrWhiteSpace(applicationName))
            return (false, "Application name is required");
            
        if (applicationName != applicationName.Trim())
            return (false, "Application name cannot have leading or trailing spaces");
            
        if (!ValidFormat.IsMatch(applicationName))
            return (false, "Application name must be 3-50 characters and contain only letters, numbers, spaces, hyphens, and underscores");
            
        if (ReservedNames.Contains(applicationName.ToLowerInvariant()))
            return (false, $"'{applicationName}' is a reserved name and cannot be used");
            
        return (true, string.Empty);
    }
}
```

### 2. **Application Registry (Optional)**

**Concept:**
- Maintain a registry of valid application names
- Validate against known applications during requests
- Prevent typos and unauthorized application creation

**Implementation Considerations:**
- Could be stored in Azure Table Storage or configuration
- Requires management process for adding new applications
- May add complexity that outweighs benefits for current use case

---

## Management Discussion Points

### **Questions for Management:**

1. **Validation Strictness:**
   - Should we enforce strict character restrictions?
   - Are there specific naming conventions to follow?
   - Should we prevent certain words or patterns?

2. **Application Registry:**
   - Do we want a pre-approved list of application names?
   - Who would manage the application registry?
   - How should new applications be approved and added?

3. **Migration Impact:**
   - Are there existing applications with names that wouldn't meet new rules?
   - Should we grandfather existing applications?
   - What's the migration timeline if validation changes?

4. **Error Handling:**
   - Should invalid application names be logged for security monitoring?
   - How should we handle validation failures in production?
   - Should we provide suggestions for valid names?

---

## Implementation Plan

### **Phase 1: Basic Enhanced Validation (Low Risk)**
- Implement character restrictions and length limits
- Add reserved name checking
- Update error messages to be more descriptive
- Add validation to all functions that accept applicationName

### **Phase 2: Application Registry (If Approved)**
- Design application registry storage
- Implement registry validation logic
- Create management interface for registry updates
- Migrate existing applications to registry

### **Phase 3: Monitoring and Alerting**
- Add security logging for validation failures
- Implement alerting for repeated invalid attempts
- Create dashboard for application name usage patterns

---

## Files to Modify

**Core Validation:**
- `Shared/ApplicationNameValidator.cs` (new file)
- `Shared/ConfigurationValidator.cs` (add application name validation)

**Function Updates:**
- `PasswordFunction.cs` - Add validation to all operations
- `RegistrationFunction.cs` - Add validation to registration
- `InvitationFunction.cs` - Add validation to invitation operations
- `UtilityFunction.cs` - Add validation where applicable

**Configuration:**
- `Shared/ConfigurationOptions.cs` - Add validation configuration options
- `local.settings.json` - Add validation settings

---

## Testing Considerations

**Test Cases:**
- Valid application names (various formats)
- Invalid characters and formats
- Reserved names
- Length boundary conditions
- Null/empty/whitespace inputs
- Case sensitivity handling

**Integration Testing:**
- Ensure existing applications continue to work
- Verify error messages are user-friendly
- Test with Power Pages integration
- Validate logging and monitoring

---

## Security Benefits

**Improved Application Isolation:**
- Prevents accidental cross-application data access
- Reduces risk of configuration errors
- Ensures consistent application naming

**Attack Surface Reduction:**
- Prevents injection of malicious application names
- Reduces risk of directory traversal attempts
- Limits potential for social engineering

**Operational Benefits:**
- Clearer error messages for troubleshooting
- Consistent application naming across system
- Better audit trail and logging

---

## Recommendation

**Immediate Action:** Implement Phase 1 (basic enhanced validation) as it provides security benefits with minimal risk and no management dependencies.

**Management Discussion:** Schedule discussion on application registry requirements and naming conventions before implementing Phase 2.

**Timeline:** Phase 1 can be implemented in 1-2 hours of development time with minimal testing overhead.
