# PowerPagesCustomAuth Configuration Variables Reference

**Date:** January 2025 (Updated)
**System:** PowerPagesCustomAuth Azure Functions
**Purpose:** Comprehensive catalog of all configuration variables across the system

**Note:** This document reflects the simplified configuration validation approach implemented in January 2025.

---

## **📋 Password Policy Configuration Analysis**

### **Current Configuration Status:**

| **Requirement** | **Current Location** | **Variable Name** | **Configurable** | **Optimal Location** |
|-----------------|---------------------|-------------------|------------------|---------------------|
| **Password Expiration (90 days)** | Azure Function App Settings | `PASSWORD_EXPIRATION_DAYS` | ✅ Yes | ✅ Current |
| **Minimum Length (8 characters)** | Hardcoded in JavaScript | `minLength = 8` | ❌ No | 🔄 Should be configurable |
| **Complexity Requirements** | Hardcoded in JavaScript | Regex patterns | ❌ No | 🔄 Should be configurable |
| **Password History (12 passwords)** | Azure Function App Settings | `PasswordHistory:MaxCount` | ✅ Yes | ✅ Current |
| **Account Lockout (5 attempts)** | Entra External ID Portal | Manual configuration | ⚠️ Portal only | ✅ Current |
| **Password Warning (15 days)** | Azure Function App Settings | `PASSWORD_WARNING_DAYS` | ✅ Yes | ✅ Current |

### **Recommendations:**
- **Move password validation rules** from hardcoded JavaScript to configurable environment variables
- **Create unified password policy configuration** section in Azure Function App Settings
- **Maintain Entra External ID lockout** configuration in Azure Portal (native capability)

---

## **🔧 Azure Function Environment Variables**

### **Password Policy Settings**
| **Variable** | **Current Value** | **Purpose** | **Required** | **Location** |
|--------------|-------------------|-------------|--------------|--------------|
| `PASSWORD_EXPIRATION_DAYS` | `"90"` | Password expiration period | ✅ Yes | local.settings.json, Azure App Settings |
| `PASSWORD_WARNING_DAYS` | `"15"` | Warning notification period | ✅ Yes | local.settings.json, Azure App Settings |
| `PasswordHistory:MaxCount` | `"12"` | Number of passwords to remember | ✅ Yes | local.settings.json, Azure App Settings |
| `PasswordHistory:WorkFactor` | `"12"` | BCrypt hashing work factor | ✅ Yes | local.settings.json, Azure App Settings |

### **Entra External ID Integration**
| **Variable** | **Purpose** | **Required** | **Location** |
|--------------|-------------|--------------|--------------|
| `EntraExternalID:TenantId` | Entra External ID tenant identifier | ✅ Yes | Local: Direct value, Azure: App Settings |
| `EntraExternalID:ClientId` | Application client ID | ✅ Yes | Local: Direct value, Azure: App Settings |
| `EntraExternalID:ClientSecret` | Application client secret | ✅ Yes | Local: Direct value, Azure: Key Vault (required) |
| `EntraExternalID:DefaultDomain` | Default tenant domain | ✅ Yes | Azure App Settings |

### **Storage Configuration**
| **Variable** | **Purpose** | **Required** | **Location** |
|--------------|-------------|--------------|--------------|
| `AzureWebJobsStorage` | Primary storage connection | ✅ Yes | Azure App Settings |
| `Storage:ConnectionString` | Alternative storage connection | ⚠️ Fallback | Azure App Settings |

### **SendGrid Email Configuration**
| **Variable** | **Purpose** | **Required** | **Location** |
|--------------|-------------|--------------|--------------|
| `SendGrid:ApiKey` | SendGrid API authentication | ✅ Yes | Local: Direct value, Azure: Key Vault (required) |
| `SendGrid:FromEmail` | Sender email address | ✅ Yes | Azure App Settings |
| `SendGrid:PasswordResetTemplateId` | Password reset email template | ✅ Yes | Azure App Settings |
| `SendGrid:PasswordChangedTemplateId` | Password changed notification template | ✅ Yes | Azure App Settings |
| `SendGrid:UserInvitationTemplateId` | User invitation email template | ✅ Yes | Azure App Settings |
| `SendGrid:AccountCreatedTemplateId` | Account creation notification template | ✅ Yes | Azure App Settings |
| `SendGrid:PasswordExpirationTemplateId` | Password expiration warning template | ✅ Yes | Azure App Settings |
| `SendGrid:PasswordExpiredTemplateId` | Password expired notification template | ✅ Yes | Azure App Settings |

### **Application Configuration**
| **Variable** | **Purpose** | **Required** | **Location** |
|--------------|-------------|--------------|--------------|
| `ApplicationName` | Application display name | ✅ Yes | Azure App Settings |
| `RateLimit:MaxRequestsPerMinute` | API rate limiting threshold | ⚠️ Optional (default: 60) | Azure App Settings |
| `KeyVaultUrl` | Azure Key Vault URL | ⚠️ Optional (local), Required (Azure) | Azure App Settings |

### **URL Configuration**
| **Variable** | **Purpose** | **Required** | **Location** |
|--------------|-------------|--------------|--------------|
| `PasswordReset:BaseUrl` | Password reset page URL | ✅ Yes | Azure App Settings |
| `AccountRegistration:BaseUrl` | Registration page URL | ✅ Yes | Azure App Settings |
| `Invitation:TokenExpirationDays` | Invitation token validity period | ✅ Yes | Azure App Settings |

---

## **🌐 Power Pages Configuration Variables**

### **Meta Tag Configuration (Recommended Pattern)**
| **Meta Tag Name** | **Liquid Template** | **Purpose** | **Required** |
|-------------------|---------------------|-------------|--------------|
| `azure-function-url` | `{{ settings['AzureFunctionUrl'] }}` | Azure Function base URL | ✅ Yes |
| `application-name` | `{{ settings['ApplicationName'] }}` | Application display name | ✅ Yes |
| `password-function-key` | `{{ settings['Password Function Key'] }}` | PasswordService function key | ✅ Yes |
| `registration-function-key` | `{{ settings['Registration Function Key'] }}` | RegistrationService function key | ✅ Yes |
| `invitation-function-key` | `{{ settings['Invitation Function Key'] }}` | InvitationService function key | ✅ Yes |
| `authentication-function-key` | `{{ settings['Authentication Function Key'] }}` | AuthenticationService function key | ✅ Yes |
| `msal-client-id` | `{{ settings['MSALClientId'] }}` | Entra External ID client ID | ✅ Yes |
| `msal-tenant-id` | `{{ settings['MSALTenantId'] }}` | Entra External ID tenant ID | ✅ Yes |
| `entra-tenant-domain` | `{{ settings['EntraTenantDomain'] }}` | Entra External ID domain | ✅ Yes |

### **Power Pages Site Settings**
| **Setting Name** | **Purpose** | **Required** | **Example Value** |
|------------------|-------------|--------------|-------------------|
| `AzureFunctionUrl` | Base URL for Azure Functions | ✅ Yes | `https://your-app.azurewebsites.net/api` |
| `ApplicationName` | Application display name | ✅ Yes | `Customer Portal` |
| `MSALClientId` | Entra External ID client ID | ✅ Yes | `12345678-1234-1234-1234-123456789012` |
| `MSALTenantId` | Entra External ID tenant ID | ✅ Yes | `87654321-4321-4321-4321-210987654321` |
| `EntraTenantDomain` | Entra External ID domain | ✅ Yes | `yourtenant.onmicrosoft.com` |
| `Password Function Key` | PasswordService function key | ✅ Yes | `function-key-value` |
| `Registration Function Key` | RegistrationService function key | ✅ Yes | `function-key-value` |
| `Invitation Function Key` | InvitationService function key | ✅ Yes | `function-key-value` |
| `Authentication Function Key` | AuthenticationService function key | ✅ Yes | `function-key-value` |

### **Authentication Settings (Power Pages)**
| **Setting Name** | **Recommended Value** | **Purpose** |
|------------------|----------------------|-------------|
| `Authentication/Registration/Enabled` | `false` | Disable native registration |
| `Authentication/Registration/RequiresConfirmation` | `false` | Disable email confirmation |
| `Authentication/Registration/RequiresInvitation` | `false` | Disable invitation requirement |
| `Authentication/PasswordReset/Enabled` | `false` | Disable native password reset |
| `Authentication/PasswordChange/Enabled` | `false` | Disable native password change |
| `Authentication/LogoutPath` | `/.auth/logout` | Standard logout path |

---

## **📧 SendGrid Template Variables**

### **Password Reset Template Variables**
| **Variable** | **Purpose** | **Example Value** |
|--------------|-------------|-------------------|
| `applicationName` | Application display name | `Customer Portal` |
| `resetUrl` | Password reset URL with token | `https://site.com/reset?token=abc123` |
| `websiteUrl` | Base website URL | `https://site.com` |
| `email` | User's email address | `<EMAIL>` |
| `correlationId` | Request correlation ID | `guid-value` |

### **Password Expiration Template Variables**
| **Variable** | **Purpose** | **Example Value** |
|--------------|-------------|-------------------|
| `applicationName` | Application display name | `Customer Portal` |
| `daysUntilExpiration` | Days until password expires | `7` |
| `websiteUrl` | Base website URL | `https://site.com` |
| `forgotPasswordUrl` | Password reset page URL | `https://site.com/reset` |
| `email` | User's email address | `<EMAIL>` |
| `notificationDate` | Notification date | `2024-08-14` |
| `correlationId` | Request correlation ID | `guid-value` |
| `absenceWarning` | User absent during warning period | `true/false` |

### **User Invitation Template Variables**
| **Variable** | **Purpose** | **Example Value** |
|--------------|-------------|-------------------|
| `applicationName` | Application display name | `Customer Portal` |
| `invitationUrl` | Registration URL with token | `https://site.com/register?token=abc123` |
| `websiteUrl` | Base website URL | `https://site.com` |
| `email` | Invited user's email | `<EMAIL>` |
| `correlationId` | Request correlation ID | `guid-value` |

---

## **🔐 Entra External ID Settings**

### **Azure Portal Configuration**
| **Setting** | **Location** | **Recommended Value** | **Purpose** |
|-------------|--------------|----------------------|-------------|
| **Account Lockout Threshold** | Authentication methods > Password protection | `5 failed sign-ins` | Password policy compliance |
| **Lockout Duration** | Authentication methods > Password protection | `60 seconds` (minimum) | Account security |
| **Sign-up Policy** | User flows | `isSignUpAllowed: false` | Force custom registration |
| **Password Complexity** | User flows > Password configuration | Use custom validation | Maintain consistency |

### **Application Registration Settings**
| **Setting** | **Purpose** | **Required** |
|-------------|-------------|--------------|
| **Client ID** | Application identifier | ✅ Yes |
| **Client Secret** | Application authentication | ✅ Yes |
| **Redirect URIs** | Power Pages authentication endpoints | ✅ Yes |
| **API Permissions** | Graph API access for user management | ✅ Yes |

---

## **❌ Hardcoded Values (Should Be Configurable)**

### **JavaScript Password Validation (Power Pages)**
**Files:** `registration.js`, `reset-password.js`, `forgot-password.js`

| **Hardcoded Value** | **Current Location** | **Should Be** | **Priority** |
|---------------------|---------------------|---------------|--------------|
| `minLength = 8` | Multiple JS files | Environment variable | 🔴 High |
| Complexity regex patterns | Multiple JS files | Environment variable | 🔴 High |
| Verification code length `6` | Multiple JS files | Environment variable | 🟡 Medium |
| Input length limits `256` | Multiple JS files | Environment variable | 🟡 Medium |

### **Recommended New Environment Variables**
```json
{
  "PASSWORD_MIN_LENGTH": "8",
  "PASSWORD_REQUIRE_UPPERCASE": "true",
  "PASSWORD_REQUIRE_LOWERCASE": "true", 
  "PASSWORD_REQUIRE_NUMBERS": "true",
  "PASSWORD_REQUIRE_SPECIAL_CHARS": "true",
  "VERIFICATION_CODE_LENGTH": "6",
  "INPUT_MAX_LENGTH": "256"
}
```

---

## **🎯 Configuration Best Practices**

### **Security Recommendations**
1. **Store secrets in Azure Key Vault**: `EntraExternalID:ClientSecret`, `SendGrid:ApiKey`
2. **Use environment variables for non-secrets**: URLs, display names, timeouts
3. **Validate configuration on startup**: Check for required variables
4. **Use meta tags for Power Pages**: Proven reliable method for configuration

### **Deployment Recommendations**
1. **Separate dev/staging/prod configurations**: Different Key Vaults and App Settings
2. **Document all required variables**: This reference document
3. **Test configuration extraction**: Browser console validation
4. **Monitor configuration changes**: Application Insights logging

### **Maintenance Recommendations**
1. **Regular configuration audits**: Ensure all variables are documented
2. **Version control configuration templates**: Track changes over time
3. **Automate configuration deployment**: ARM templates or Bicep
4. **Backup configuration settings**: Export before major changes
