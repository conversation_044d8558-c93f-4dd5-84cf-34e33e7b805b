using Microsoft.Azure.Functions.Worker.Http;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;

namespace PasswordHistoryValidator.Shared;

public static class HttpResponseHelper
{
    public static async Task<HttpResponseData> CreateJsonResponse<T>(
        HttpRequestData req, 
        T data, 
        HttpStatusCode statusCode, 
        string correlationId, 
        JsonSerializerOptions jsonOptions)
    {
        var response = req.CreateResponse(statusCode);
        var result = new
        {
            data = data,
            correlationId = correlationId,
            timestamp = DateTime.UtcNow
        };

        response.Headers.Add("Content-Type", "application/json");
        var json = JsonSerializer.Serialize(result, jsonOptions);
        await response.WriteStringAsync(json);
        AddCorsHeaders(response);
        return response;
    }

    public static async Task<HttpResponseData> CreateErrorResponse(
        HttpRequestData req, 
        string message, 
        string correlationId, 
        JsonSerializerOptions jsonOptions, 
        HttpStatusCode statusCode = HttpStatusCode.BadRequest)
    {
        var response = req.CreateResponse(statusCode);
        var result = new
        {
            success = false,
            message = message,
            correlationId = correlationId,
            timestamp = DateTime.UtcNow
        };

        response.Headers.Add("Content-Type", "application/json");
        var json = JsonSerializer.Serialize(result, jsonOptions);
        await response.WriteStringAsync(json);
        AddCorsHeaders(response);
        return response;
    }

    public static HttpResponseData CreateCorsResponse(HttpRequestData req)
    {
        var response = req.CreateResponse(HttpStatusCode.OK);
        AddCorsHeaders(response);
        return response;
    }


    public static async Task<HttpResponseData?> CheckRateLimit(
        HttpRequestData req,
        RateLimitService rateLimitService,
        string operation,
        string correlationId,
        JsonSerializerOptions jsonOptions,
        CancellationToken cancellationToken = default)
    {
        var clientId = Utilities.GetClientIdentifier(req);
        var rateLimitInfo = await rateLimitService.CheckRateLimitAsync(clientId, operation, cancellationToken);

        if (!rateLimitInfo.IsAllowed)
        {
            return await CreateJsonResponse(req, new
            {
                success = false,
                message = "Rate limit exceeded. Please try again later.",
                errorCode = "RateLimitExceeded",
                retryAfter = rateLimitInfo.WindowResetTime
            }, HttpStatusCode.TooManyRequests, correlationId, jsonOptions);
        }

        return null;
    }

    public static async Task<HttpResponseData?> ValidateRequestData<T>(
        HttpRequestData req, 
        T data, 
        string correlationId, 
        JsonSerializerOptions jsonOptions) 
        where T : class
    {
        var validationResults = new List<ValidationResult>();
        var validationContext = new ValidationContext(data);
        if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
        {
            var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
            return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId, jsonOptions);
        }
        return null;
    }

    private static void AddCorsHeaders(HttpResponseData response)
    {
        response.Headers.Add("Access-Control-Allow-Origin", "*");
        response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
    }
}
