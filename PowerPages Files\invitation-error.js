// Unified Notification System for Power Pages
const NotificationSystem = {
  config: { defaultTimeout: 5000, fadeInDuration: 300, fadeOutDuration: 300, autoHide: true, showIcons: true },
  icons: { success: 'fas fa-check-circle', error: 'fas fa-exclamation-circle', warning: 'fas fa-exclamation-triangle', info: 'fas fa-info-circle', loading: 'fas fa-spinner fa-spin' },

  show(type, message, options = {}) {
    const settings = { ...this.config, ...options };
    const container = this.getNotificationContainer(settings.containerId);
    if (!container) {
      console.warn('Notification container not found');
      return;
    }

    if (settings.clearExisting !== false) this.clear(container);

    const notification = this.createNotification(type, message, settings);
    container.appendChild(notification);
    notification.classList.add('notification-fade-in');

    if (settings.autoHide && settings.timeout > 0) {
      setTimeout(() => this.hide(notification), settings.timeout || this.config.defaultTimeout);
    }

    this.setAriaLiveRegion(notification, type);
    return notification;
  },

  showSuccess(message, options = {}) {
    return this.show('success', message, { ...options, timeout: options.timeout || 4000, title: options.title || 'Success' });
  },

  showError(message, options = {}) {
    return this.show('error', message, { ...options, timeout: options.timeout || 0, title: options.title || 'Error' });
  },

  showWarning(message, options = {}) {
    return this.show('warning', message, { ...options, timeout: options.timeout || 6000, title: options.title || 'Warning' });
  },

  showInfo(message, options = {}) {
    return this.show('info', message, { ...options, timeout: options.timeout || 5000, title: options.title || 'Information' });
  },

  showLoading(message, options = {}) {
    return this.show('loading', message, { ...options, timeout: 0, title: options.title || 'Loading', autoHide: false });
  },

  createNotification(type, message, settings) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
    notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

    let content = '';
    if (settings.showIcons && this.icons[type]) {
      content += `<div class="notification-icon"><i class="${this.icons[type]}" aria-hidden="true"></i></div>`;
    }

    content += '<div class="notification-content">';
    if (settings.title) {
      content += `<div class="notification-title">${this.escapeHtml(settings.title)}</div>`;
    }
    content += `<div class="notification-message">${this.escapeHtml(message)}</div></div>`;

    notification.innerHTML = content;
    if (settings.compact) notification.classList.add('notification-compact');
    return notification;
  },

  getNotificationContainer(containerId) {
    if (containerId) return document.getElementById(containerId);

    const commonIds = ['notificationContainer', 'messageContainer', 'alertContainer'];
    for (const id of commonIds) {
      const container = document.getElementById(id);
      if (container) return container;
    }

    const jquerySelectors = ['#errorMessage', '#successMessage', '#messageContent'];
    for (const selector of jquerySelectors) {
      const element = $(selector);
      if (element.length > 0) return element[0];
    }

    return this.createDefaultContainer();
  },

  createDefaultContainer() {
    const container = document.createElement('div');
    container.id = 'notificationContainer';
    container.className = 'notification-container';

    const mainContent = document.querySelector('.card-body, .container, main, body');
    if (mainContent) {
      mainContent.insertBefore(container, mainContent.firstChild);
      return container;
    }
    return null;
  },

  hide(notification) {
    if (!notification || !notification.parentNode) return;
    notification.style.transition = `opacity ${this.config.fadeOutDuration}ms ease`;
    notification.style.opacity = '0';
    setTimeout(() => {
      if (notification.parentNode) notification.parentNode.removeChild(notification);
    }, this.config.fadeOutDuration);
  },

  clear(container) {
    if (!container) container = this.getNotificationContainer();
    if (container) {
      const notifications = container.querySelectorAll('.notification, .message, .alert');
      notifications.forEach(notification => this.hide(notification));
    }
    $('#errorMessage, #successMessage').hide();
  },

  setAriaLiveRegion(notification, type) {
    if (type === 'error') {
      notification.setAttribute('aria-live', 'assertive');
      notification.setAttribute('role', 'alert');
    } else {
      notification.setAttribute('aria-live', 'polite');
      notification.setAttribute('role', 'status');
    }
  },

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  },

  // Consolidated error handling with preserved functionality
  handleError(error, context = {}) {
    console.error('Error in context:', context, error);
    if (error.status) return this.handleHttpError(error, context);

    const errorTypes = {
      fetch: () => error instanceof TypeError && error.message.includes('fetch'),
      timeout: () => error.name === 'AbortError' || error.message.includes('timeout'),
      config: () => error.message.includes('configuration') || error.message.includes('missing'),
      rateLimit: () => error.message.includes('rate limit') || error.message.includes('429'),
      auth: () => error.message.includes('401') || error.message.includes('unauthorized')
    };

    const errorMessages = {
      fetch: { msg: 'Network connection failed. Please check your internet connection and try again.', title: 'Connection Error' },
      timeout: { msg: 'Request timed out. Please check your connection and try again. If the problem persists, try refreshing the page.', title: 'Request Timeout' },
      config: { msg: 'System configuration error. Please contact support if this persists.', title: 'Configuration Error' },
      rateLimit: { msg: error.message, title: 'Rate Limit', timeout: 10000 },
      auth: { msg: 'Authentication failed. Please refresh the page and try again.', title: 'Authentication Error' }
    };

    for (const [type, check] of Object.entries(errorTypes)) {
      if (check()) {
        const { msg, title, timeout = 0 } = errorMessages[type];
        return type === 'rateLimit' ? this.showWarning(msg, { title, timeout }) : this.showError(msg, { title, timeout });
      }
    }

    return this.showError(error.message || 'An unexpected error occurred. Please try again.', { title: 'Error', timeout: 0 });
  },

  handleHttpError(error, context = {}) {
    const httpErrors = {
      400: { msg: 'Invalid request. Please check your input and try again.', title: 'Invalid Request' },
      401: { msg: 'Authentication required. Please refresh the page and try again.', title: 'Authentication Required' },
      403: { msg: 'Access denied. You do not have permission to perform this action.', title: 'Access Denied' },
      404: { msg: 'Service not found. Please contact support if this persists.', title: 'Service Unavailable' },
      429: { msg: 'Too many requests. Please wait a moment before trying again.', title: 'Rate Limit Exceeded', timeout: 15000 },
      500: { msg: 'A server error occurred. Please try again later or contact support.', title: 'Server Error' }
    };

    const status = error.status;

    if (status === 409) {
      const conflictMsg = error.message || 'A conflict occurred. Please review your input.';
      if (conflictMsg.toLowerCase().includes('email') &&
          (conflictMsg.toLowerCase().includes('exists') || conflictMsg.toLowerCase().includes('already') || conflictMsg.toLowerCase().includes('duplicate'))) {
        return this.showError('An account with this email address already exists. Please try signing in instead.', { title: 'Account Already Exists', timeout: 0 });
      }
      return this.showError(conflictMsg, { title: 'Conflict', timeout: 0 });
    }

    const errorInfo = httpErrors[status] || (status >= 500 ? httpErrors[500] : { msg: 'Request failed. Please try again or contact support if the problem persists.', title: 'Request Failed' });
    const { msg, title, timeout = 0 } = errorInfo;

    return status === 429 ? this.showWarning(msg, { title, timeout }) : this.showError(msg, { title, timeout });
  },

  validateConfiguration(requiredConfig = []) {
    const missing = requiredConfig.filter(key => !window.appConfig || !window.appConfig[key]);
    if (missing.length > 0) {
      this.showError(`Missing configuration: ${missing.join(', ')}. Please contact support.`, { title: 'Configuration Error', timeout: 0 });
      return false;
    }
    return true;
  },

  validateDOMElements(elementSelectors = []) {
    const missing = elementSelectors.filter(selector => !document.querySelector(selector));
    if (missing.length > 0) {
      console.error('Missing DOM elements:', missing);
      this.showError('Page elements missing. Please refresh the page.', { title: 'Page Error', timeout: 0 });
      return false;
    }
    return true;
  },

  checkBrowserCompatibility() {
    const features = [
      { check: () => typeof fetch === 'undefined', name: 'Fetch API' },
      { check: () => typeof Promise === 'undefined', name: 'Promise support' },
      { check: () => typeof URLSearchParams === 'undefined', name: 'URL Parameters' },
      { check: () => !document.querySelector || !document.querySelectorAll, name: 'Modern DOM methods' }
    ];

    try {
      if (typeof localStorage === 'undefined' || typeof sessionStorage === 'undefined') {
        features.push({ check: () => true, name: 'Local Storage' });
      }
    } catch (e) {
      features.push({ check: () => true, name: 'Local Storage' });
    }

    const missing = features.filter(f => f.check()).map(f => f.name);

    if (missing.length > 0) {
      const message = `Your browser is missing required features: ${missing.join(', ')}. Please update to a modern browser (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+) to use this application.`;
      try {
        this.showError(message, { title: 'Browser Compatibility Issue', timeout: 0 });
      } catch (e) {
        alert('Browser Compatibility Issue: ' + message);
      }
      return false;
    }
    return true;
  }
};

document.addEventListener('DOMContentLoaded', () => {
    if (!NotificationSystem.checkBrowserCompatibility()) return;
    initializeErrorPage();
    loadErrorDetails();
});

function initializeErrorPage() {
    if (!window.appConfig) {
        NotificationSystem.showError('System configuration missing. Please contact support.', { title: 'Configuration Error', timeout: 0 });
    }
}

function loadErrorDetails() {
    try {
        const errorData = sessionStorage.getItem('invitationError');
        if (errorData) {
            const error = JSON.parse(errorData);
            displayErrorDetails(error);
            sessionStorage.removeItem('invitationError');
        } else {
            const urlParams = new URLSearchParams(window.location.search);
            const errorMessage = urlParams.get('error');
            const errorCode = urlParams.get('code');
            if (errorMessage || errorCode) {
                displayErrorDetails({
                    message: errorMessage || 'Unknown error',
                    code: errorCode,
                    source: 'url-parameters',
                    timestamp: new Date().toISOString()
                });
            }
        }
    } catch (error) {
        console.error('Error loading error details:', error);
    }
}

function displayErrorDetails(errorData) {
    console.log('Displaying error details:', errorData);
    if (errorData.message) {
        const errMsg = document.getElementById('errorMessage');
        if (errMsg) {
            const msgText = errMsg.querySelector('p');
            if (msgText) msgText.textContent = errorData.message;
        }
    }
    updatePageTitle(errorData);
}

function updatePageTitle(errorData) {
    if (!errorData?.message) return;

    const message = errorData.message.toLowerCase();
    const errMsg = document.querySelector('.error-message');
    const titleEl = errMsg?.querySelector('h3');
    if (!titleEl) return;

    const titleUpdates = {
        expired: { title: 'Invitation Expired', desc: 'Your invitation link has expired. Please request a new invitation from your administrator.' },
        used: { title: 'Invitation Already Used', desc: 'This invitation has already been used to create an account. If you already have an account, please try logging in.' },
        invalid: { title: 'Invalid Invitation', desc: 'The invitation link appears to be invalid or corrupted. Please check the link and try again, or request a new invitation.' }
    };

    let updateKey = null;
    if (message.includes('expired')) updateKey = 'expired';
    else if (message.includes('used') || message.includes('already')) updateKey = 'used';
    else if (message.includes('invalid') || message.includes('not found')) updateKey = 'invalid';

    if (updateKey) {
        const { title, desc } = titleUpdates[updateKey];
        titleEl.textContent = title;
        const descEl = errMsg.querySelector('p');
        if (descEl) descEl.textContent = desc;
    }
}

const sanitizeInput = input => typeof input === 'string' ? input.trim().replace(/[<>"'&]/g, '').substring(0, 256) : '';

const logUserAction = (action, details = {}) => {
    try {
        console.log('User action:', action, details);
    } catch (error) {
        console.error('Error logging user action:', error);
    }
};

logUserAction('invitation_error_page_view', {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    referrer: document.referrer
});
