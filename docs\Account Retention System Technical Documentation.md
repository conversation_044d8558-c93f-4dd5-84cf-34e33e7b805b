# Account Retention System Technical Documentation

## Overview

The Account Retention System provides automated cleanup of inactive user accounts and their associated password history data. This system integrates with the existing PowerPagesCustomAuth infrastructure to maintain data hygiene while preserving active user accounts.

## System Architecture

### Core Components

1. **UtilityFunction.cs** - Main operation handler following existing patterns
2. **Microsoft Graph API** - Sign-in log analysis for activity detection
3. **Azure Blob Storage** - Password history data cleanup
4. **Entra External ID** - User account deletion
5. **Configuration System** - Retention period management

### Data Flow Architecture

```
HTTP Request → UtilityFunction → ProcessAccountRetentionCleanup
    ↓
Password History Blobs → GetPasswordHistoryData → Extract User IDs
    ↓
Graph API Sign-in Logs → GetLastSignInDate → Activity Analysis
    ↓
Inactive Account Detection → DeleteAccountAndCleanup
    ↓
Entra External ID Deletion + Blob Storage Cleanup → Results
```

## Technical Implementation

### Configuration Setup

**Environment Variable:**
```bash
ACCOUNT_RETENTION_DAYS=90
```

**ApplicationOptions Class:**
```csharp
public class ApplicationOptions
{
    // ... existing properties
    public int AccountRetentionDays { get; set; } = 90;
}
```

**Program.cs Configuration:**
```csharp
options.AccountRetentionDays = configuration.GetValue<int>("ACCOUNT_RETENTION_DAYS", 90);
```

### Operation Handler Pattern

The account retention system follows the established UtilityFunction operation pattern:

```csharp
// Operation switch integration
"cleanup-inactive-accounts" => await HandleAccountRetentionCleanup(req, correlationId, cancellationToken)

// Main handler method
private async Task<HttpResponseData> HandleAccountRetentionCleanup(...)
{
    var retentionDays = _applicationOptions.Value.AccountRetentionDays;
    var results = await ProcessAccountRetentionCleanup(retentionDays, cancellationToken);
    
    return await HttpResponseHelper.CreateJsonResponse(req, new
    {
        message = "Account retention cleanup completed",
        accountsProcessed = results.AccountsProcessed,
        accountsDeleted = results.AccountsDeleted,
        passwordHistoryBlobsDeleted = results.PasswordHistoryBlobsDeleted,
        retentionDays,
        timestamp = DateTime.UtcNow
    }, HttpStatusCode.OK, correlationId, _jsonOptions);
}
```

### Method Responsibilities

#### 1. ProcessAccountRetentionCleanup
**Responsibility:** Orchestrates the entire cleanup process
- Iterates through password history blobs
- Coordinates helper methods
- Tracks results and handles errors
- Follows existing pattern from `ProcessPasswordExpirationNotifications`

#### 2. GetPasswordHistoryData
**Responsibility:** Blob data retrieval and parsing
- Downloads blob content
- Deserializes JSON to PasswordHistoryStorage
- Handles blob access errors gracefully

#### 3. IsAccountInactive
**Responsibility:** Determines account activity status
- Calls GetLastSignInDate for activity data
- Calculates days since last sign-in
- Returns boolean inactivity status

#### 4. GetLastSignInDate
**Responsibility:** Graph API sign-in log analysis
- Queries AuditLogs.SignIns endpoint
- Filters by user and application context
- Returns most recent successful sign-in date
- Extends existing CheckAbsenceDuringExpirationPeriod pattern

#### 5. DeleteAccountAndCleanup
**Responsibility:** Coordinates deletion operations
- Calls Entra External ID account deletion
- Calls password history blob cleanup
- Returns tuple with operation results

#### 6. DeleteEntraExternalIdAccount
**Responsibility:** User account deletion from Entra External ID
- Searches for user by email and application
- Deletes user account via Graph API
- Handles user not found scenarios

#### 7. DeletePasswordHistoryBlob
**Responsibility:** Password history data cleanup
- Deletes associated blob storage data
- Handles blob deletion errors

## Graph API Integration

### Sign-in Log Query Pattern

```csharp
var filter = $"userPrincipalName eq '{emailEsc}' and appDisplayName eq '{appEsc}' and status/errorCode eq 0";

var signIns = await _graphServiceClient.AuditLogs.SignIns
    .GetAsync(requestConfiguration =>
    {
        requestConfiguration.QueryParameters.Filter = filter;
        requestConfiguration.QueryParameters.Top = 1;
        requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
    }, cancellationToken);
```

### User Deletion Pattern

```csharp
var filter = $"(mail eq '{emailEsc}' or userPrincipalName eq '{emailEsc}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc}')) and department eq '{appEsc}'";

var users = await _graphServiceClient.Users.GetAsync(...);
var user = users?.Value?.FirstOrDefault();

if (user?.Id != null)
{
    await _graphServiceClient.Users[user.Id].DeleteAsync(cancellationToken: cancellationToken);
}
```

### Required Permissions

- **AuditLog.Read.All** - Read sign-in logs for activity detection
- **User.ReadWrite.All** - Delete user accounts from Entra External ID
- **Directory.Read.All** - Read user information for lookup

## Error Handling Strategy

### Individual Item Processing
```csharp
try
{
    var historyData = await GetPasswordHistoryData(blobItem, cancellationToken);
    // ... processing logic
}
catch (Exception ex)
{
    _logger.LogWarning(ex, "Error processing account {BlobName} during retention cleanup", blobItem.Name);
    // Continue processing other accounts
}
```

### Graceful Degradation
- **Graph API Unavailable:** Log warning, assume accounts are active
- **User Not Found:** Log warning, continue processing
- **Blob Deletion Failure:** Log warning, account deletion still succeeds

### Logging Patterns
- **Information:** Successful account deletions with user ID and Entra ID
- **Warning:** Individual processing failures, Graph API issues
- **Error:** Critical failures in account deletion operations

## Usage Instructions

### Manual Invocation

**Local Development:**
```bash
POST http://localhost:7071/api/UtilityService?operation=cleanup-inactive-accounts
Content-Type: application/json
```

**Production:**
```bash
POST https://your-function-app.azurewebsites.net/api/UtilityService?operation=cleanup-inactive-accounts&code=your-function-key
Content-Type: application/json
```

### Response Format

```json
{
  "data": {
    "message": "Account retention cleanup completed",
    "accountsProcessed": 25,
    "accountsDeleted": 3,
    "passwordHistoryBlobsDeleted": 3,
    "retentionDays": 90,
    "timestamp": "2025-01-21T10:30:00.000Z"
  },
  "correlationId": "abc123-def456-ghi789",
  "timestamp": "2025-01-21T10:30:00.000Z"
}
```

## Integration with Existing Infrastructure

### Consistency with Existing Patterns

1. **Operation Switch Pattern:** Follows identical structure to existing operations
2. **Helper Method Organization:** Mirrors `SendExpirationNotification` pattern
3. **Error Handling:** Uses same try-catch and logging approaches
4. **Configuration Management:** Extends existing ApplicationOptions pattern
5. **Response Format:** Uses HttpResponseHelper.CreateJsonResponse
6. **Dependency Injection:** Leverages existing service registrations

### Code Quality Alignment

- **Single Responsibility:** Each method has focused, clear purpose
- **Simplicity:** Avoids over-engineering while maintaining functionality
- **Maintainability:** Clear method names and logical organization
- **Testability:** Focused methods enable easy unit testing

## Deployment Configuration

### Environment Variables

**Local Development (.env file):**
```bash
ACCOUNT_RETENTION_DAYS=90
```

**Azure App Settings:**
```bash
ACCOUNT_RETENTION_DAYS=90
```

### Validation

- **Startup Validation:** Not required (has sensible default)
- **Runtime Validation:** Minimum value check can be added if needed
- **Configuration Override:** Supports Azure Key Vault integration

## Scheduled Execution Recommendations

### Azure Logic App Integration
```json
{
  "recurrence": {
    "frequency": "Month",
    "interval": 1,
    "startTime": "2025-01-01T06:00:00Z"
  },
  "actions": {
    "http_request": {
      "type": "Http",
      "inputs": {
        "method": "POST",
        "uri": "https://your-function-app.azurewebsites.net/api/UtilityService?operation=cleanup-inactive-accounts&code=@{parameters('functionKey')}"
      }
    }
  }
}
```

### Alternative Scheduling Options
- **Azure Automation:** PowerShell runbook with HTTP request
- **External Scheduler:** Cron job or task scheduler
- **Manual Execution:** Administrative operation as needed

## Troubleshooting

### Common Issues

**1. Graph API Permission Errors**
- Verify AuditLog.Read.All and User.ReadWrite.All permissions
- Ensure admin consent is granted
- Check service principal configuration

**2. No Accounts Processed**
- Verify password history container exists
- Check blob storage connection string
- Confirm users have password history data

**3. Sign-in Data Not Found**
- Check Graph API audit log retention period
- Verify application name filtering
- Confirm sign-in logs are being generated

**4. Account Deletion Failures**
- Verify user exists in Entra External ID
- Check Graph API permissions
- Confirm department field filtering

### Monitoring and Diagnostics

**Application Insights Queries:**
```kusto
traces
| where message contains "Account retention cleanup"
| project timestamp, message, customDimensions
| order by timestamp desc
```

**Health Check Integration:**
The account retention system integrates with existing health monitoring through the UtilityFunction health check endpoint.

## Security Considerations

### Data Protection
- **Audit Trail:** All deletions logged with correlation IDs
- **Graceful Failures:** Individual failures don't affect other accounts
- **Permission Validation:** Requires explicit Graph API permissions

### Access Control
- **Function-Level Authorization:** Requires function key for access
- **Internal Operation:** Not exposed to end users
- **Administrative Control:** Manual execution or scheduled automation

## Performance Characteristics

### Processing Efficiency
- **Blob Iteration:** Processes one account at a time
- **Graph API Calls:** One sign-in query per account
- **Batch Processing:** Not implemented (follows existing patterns)

### Resource Usage
- **Memory:** Minimal (streaming blob processing)
- **Network:** Graph API calls for each account
- **Storage:** Blob deletions reduce storage usage

### Scalability Considerations
- **Large Datasets:** May require timeout adjustments
- **Rate Limiting:** Graph API throttling may apply
- **Monitoring:** Track processing times and success rates

## Complete Workflow Explanation

### 1. HTTP Request Processing
```
POST /api/UtilityService?operation=cleanup-inactive-accounts
    ↓
UtilityFunction.Run() → operation switch → HandleAccountRetentionCleanup()
```

### 2. Configuration and Initialization
```csharp
var retentionDays = _applicationOptions.Value.AccountRetentionDays; // Default: 90 days
var results = await ProcessAccountRetentionCleanup(retentionDays, cancellationToken);
```

### 3. Password History Blob Processing
```
Azure Blob Storage "passwordhistory" container
    ↓
await foreach (var blobItem in containerClient.GetBlobsAsync())
    ↓
GetPasswordHistoryData() → Download and deserialize JSON
    ↓
Extract userId (format: "ApplicationName/<EMAIL>")
```

### 4. Account Activity Analysis
```
userId → GetLastSignInDate()
    ↓
Parse applicationName and email from userId
    ↓
Graph API Query: AuditLogs.SignIns
    ↓
Filter: userPrincipalName + appDisplayName + successful logins only
    ↓
Return most recent sign-in DateTime or null
```

### 5. Inactivity Determination
```csharp
var daysSinceLastSignIn = lastSignIn.HasValue
    ? (DateTime.UtcNow - lastSignIn.Value).TotalDays
    : double.MaxValue; // No sign-in found = inactive

return daysSinceLastSignIn > retentionDays;
```

### 6. Account Deletion Workflow
```
Inactive Account Detected
    ↓
DeleteAccountAndCleanup()
    ↓
DeleteEntraExternalIdAccount() → Graph API Users.Delete()
    ↓
If successful → DeletePasswordHistoryBlob()
    ↓
Return (AccountDeleted: bool, BlobDeleted: bool)
```

### 7. Results Compilation and Response
```csharp
{
    "accountsProcessed": 25,      // Total password history blobs processed
    "accountsDeleted": 3,         // Entra External ID accounts deleted
    "passwordHistoryBlobsDeleted": 3,  // Associated blob storage cleaned up
    "retentionDays": 90,          // Configuration used
    "timestamp": "2025-01-21T10:30:00.000Z"
}
```

## Integration with PowerPagesCustomAuth Patterns

### 1. Consistent Architecture Alignment
- **Operation Handler Pattern:** Identical to existing "cleanup-tokens", "stats" operations
- **Helper Method Organization:** Follows SendExpirationNotification pattern
- **Error Handling:** Uses same try-catch and logging approaches as existing code
- **Configuration Management:** Extends ApplicationOptions following established pattern

### 2. Code Quality Consistency
- **Single Responsibility Principle:** Each method has focused, clear purpose
- **Simplicity Over Engineering:** Avoids complex abstractions
- **Maintainability:** Clear method names and logical organization
- **Existing Dependency Usage:** Leverages injected services without new dependencies

### 3. Operational Consistency
- **HTTP Response Format:** Uses HttpResponseHelper.CreateJsonResponse
- **Correlation ID Tracking:** Maintains existing tracing patterns
- **CORS Handling:** Automatic through existing infrastructure
- **Rate Limiting:** Not required (internal maintenance operation)

## Implementation Notes

### Key Design Decisions

1. **Method Separation:** Broke down complex operations into focused helper methods
2. **Error Isolation:** Individual account failures don't stop overall processing
3. **Graceful Degradation:** System continues if Graph API is unavailable
4. **Data Consistency:** Account deletion only proceeds if Entra External ID deletion succeeds

### Consistency with Existing Codebase

1. **Follows Existing Patterns:**
   - Operation switch structure identical to existing operations
   - Helper method organization mirrors SendExpirationNotification pattern
   - Error handling uses same try-catch and logging approaches
   - Configuration extends ApplicationOptions following established pattern

2. **Maintains Code Quality Standards:**
   - Single responsibility principle for each method
   - Clear, descriptive method names
   - Consistent error handling and logging
   - Proper separation of concerns

3. **Preserves Simplicity Principles:**
   - No over-engineering or complex abstractions
   - Straightforward logic flow
   - Minimal dependencies (uses existing injected services)
   - Clear, readable implementation

### Technical Integration Points

1. **Configuration System:** Seamlessly extends existing ApplicationOptions
2. **Dependency Injection:** Uses existing service registrations
3. **Error Handling:** Follows established patterns from other operations
4. **Logging:** Maintains correlation ID tracking and structured logging
5. **Response Format:** Uses existing HttpResponseHelper patterns

This implementation demonstrates how new functionality can be added to the PowerPagesCustomAuth system while maintaining complete consistency with existing architectural patterns and code quality standards.
