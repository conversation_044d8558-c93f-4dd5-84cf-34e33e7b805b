# Entra External ID Native Password Policy Capabilities Analysis

**Date:** August 14th, 2024
**System:** PowerPagesCustomAuth Azure Functions Integration
**Scope:** Technical Implementation Guide for Native Entra External ID Capabilities
**Document Type:** Technical Reference

> **📋 Related Documents:**
> - **Strategic Overview**: `docs/ASSESSMENTS/PowerPagesCustomAuth Password Policy Comprehensive Analysis.md`
> - **Configuration Reference**: `docs/PowerPagesCustomAuth Configuration Variables Reference.md`
> - **Deployment Guide**: `docs/Production Deployment Setup Guide.md`

## Executive Summary

This analysis examines Microsoft Entra External ID's native capabilities for implementing password policy requirements identified as gaps in our PowerPagesCustomAuth system. The findings reveal **mixed native support** with some requirements fully supported by Entra External ID, while others require custom implementation in our Azure Functions.

**Key Finding:** Entra External ID provides **strong native support for account lockout and password expiration enforcement** but has **limited configurability** for specific thresholds and durations required by our policy.

---

## 1. Account Lockout (5 Failed Attempts)

### **Native Capabilities: ✅ FULLY SUPPORTED**

**Entra External ID Smart Lockout:**
- **Default Configuration**: 10 failed attempts (Azure Public), 3 failed attempts (Azure US Government)
- **Customizable Threshold**: Can be configured down to **minimum 1 failed attempt**
- **Intelligent Lockout**: Differentiates between familiar and unfamiliar locations
- **Always Active**: Smart lockout is enabled by default for all Entra External ID tenants

### **Configuration Options**

**Azure Portal Configuration:**
```
Entra ID > Authentication methods > Password protection
- Lockout threshold: [1-999] failed sign-ins
- Default: 10 (Azure Public), 3 (Azure US Government)
```

**PowerShell Configuration:**
```powershell
# Requires Microsoft Entra ID P1 or higher licenses
Connect-Entra -Scopes 'Policy.ReadWrite.ConditionalAccess'
# Configuration through Authentication Policy Administrator role
```

**Graph API Endpoint:**
- **Endpoint**: Not directly configurable via Graph API
- **Management**: Through Azure Portal or PowerShell only
- **Monitoring**: Available via `auditLogs/signIns` endpoint

### **Integration with PowerPagesCustomAuth**

**Hybrid Approach Recommended:**
1. **Entra External ID**: Set lockout threshold to 5 failed attempts
2. **Azure Functions**: Monitor lockout events via Graph API sign-in logs
3. **Power Pages**: Display appropriate lockout messages

**Implementation Steps:**
```powershell
# Set lockout threshold to 5 attempts
Set-EntraAuthenticationMethodPolicy -LockoutThreshold 5
```

### **Gaps Where Custom Implementation Needed**
- **Application-Specific Lockout**: Entra External ID lockout is tenant-wide, not application-specific
- **Custom Lockout Logic**: Our system may need application-aware lockout tracking

---

## 2. Lockout Duration (30 Minutes)

### **Native Capabilities: 🟡 PARTIALLY SUPPORTED**

**Entra External ID Smart Lockout Duration:**
- **Default Duration**: 60 seconds (1 minute) for first lockout
- **Progressive Lockout**: Duration increases with subsequent failed attempts
- **Minimum Configuration**: 60 seconds (cannot be set lower)
- **Maximum Configuration**: No documented upper limit

### **Configuration Options**

**Azure Portal Configuration:**
```
Entra ID > Authentication methods > Password protection
- Lockout duration in seconds: [60-∞]
- Default: 60 seconds
```

**Limitation:**
- **Cannot set to 30 minutes directly**: Entra External ID uses progressive lockout
- **First lockout**: Always starts at 60 seconds minimum
- **30-minute lockout**: Would only occur after multiple repeated lockout cycles

### **Integration Possibilities**

**Conditional Access Policy Approach:**
```powershell
# Create conditional access policy for extended lockout
$conditions = New-Object -TypeName Microsoft.Open.MSGraph.Model.ConditionalAccessConditionSet
$conditions.SignInRiskLevels = @("high")
$controls = New-Object -TypeName Microsoft.Open.MSGraph.Model.ConditionalAccessGrantControls
$controls.BuiltInControls = @("block")
```

**Custom Implementation Required:**
- **Azure Functions**: Track lockout events and implement 30-minute application-specific lockout
- **Power Pages**: Respect both Entra External ID lockout and custom 30-minute lockout

### **Recommended Approach**
1. **Entra External ID**: Configure 60-second base lockout
2. **Azure Functions**: Implement additional 30-minute lockout logic
3. **Integration**: Monitor sign-in logs for lockout events

---

## 3. Password Expiration Enforcement

### **Native Capabilities: ✅ FULLY SUPPORTED**

**ForceChangePasswordNextSignIn Property:**
- **Graph API Support**: Full support via `passwordProfile.forceChangePasswordNextSignIn`
- **Enforcement**: Blocks login until password is changed
- **Integration**: Works with standard Entra External ID authentication flows

### **Graph API Implementation**

**Update User to Force Password Change:**
```http
PATCH https://graph.microsoft.com/v1.0/users/{userId}
Content-Type: application/json

{
  "passwordProfile": {
    "forceChangePasswordNextSignIn": true
  }
}
```

**Required Permissions:**
- **Delegated**: `User.ReadWrite.All` or `User-PasswordProfile.ReadWrite.All`
- **Application**: `User.ReadWrite.All`
- **Role Required**: User Administrator (minimum)

### **Integration with PowerPagesCustomAuth**

**Seamless Integration Possible:**
1. **Azure Functions**: Detect expired passwords via `PasswordHistoryService`
2. **Graph API**: Set `forceChangePasswordNextSignIn = true`
3. **Entra External ID**: Enforces password change on next login
4. **Power Pages**: Redirects to password change flow

**Implementation Example:**
```csharp
// In PasswordFunction.cs
private async Task<bool> EnforcePasswordExpiration(string userId)
{
    var userUpdate = new User
    {
        PasswordProfile = new PasswordProfile
        {
            ForceChangePasswordNextSignIn = true
        }
    };
    
    await _graphServiceClient.Users[userId].PatchAsync(userUpdate);
    return true;
}
```

### **Configuration Options**
- **Tenant-Level**: Password expiration policies via `Get-EntraPasswordPolicy`
- **User-Level**: Individual user enforcement via Graph API
- **Application-Level**: Custom logic in Azure Functions

---

## 4. Login Tracking for Absence Handling

### **Native Capabilities: ✅ EXCELLENT SUPPORT**

**Sign-in Logs via Graph API:**
- **Endpoint**: `https://graph.microsoft.com/v1.0/auditLogs/signIns`
- **Data Retention**: 30 days (free), 90 days (P1), 180 days (P2)
- **Rich Data**: Includes timestamps, application context, success/failure status
- **Real-time**: Near real-time availability of sign-in events

### **Available Sign-in Data**

**Key Properties for Absence Detection:**
```json
{
  "createdDateTime": "2024-08-14T10:30:00Z",
  "userDisplayName": "John Doe",
  "userPrincipalName": "<EMAIL>",
  "appDisplayName": "Customer Portal",
  "status": {
    "errorCode": 0,
    "failureReason": null,
    "additionalDetails": null
  },
  "location": {
    "city": "Seattle",
    "state": "Washington",
    "countryOrRegion": "US"
  }
}
```

### **Graph API Queries for Absence Detection**

**Get User's Recent Sign-ins:**
```http
GET https://graph.microsoft.com/v1.0/auditLogs/signIns?$filter=userId eq '{userId}' and createdDateTime ge {date}&$orderby=createdDateTime desc
```

**PowerShell Example:**
```powershell
Connect-Entra -Scopes 'AuditLog.Read.All','Directory.Read.All'
Get-EntraAuditSignInLog -Filter "userId eq 'user-id' and createdDateTime ge 2024-07-01T00:00:00Z" | 
Select-Object createdDateTime, userDisplayName, appDisplayName, status
```

### **Integration with PowerPagesCustomAuth**

**Comprehensive Absence Tracking:**
1. **Azure Functions**: Query sign-in logs via Graph API
2. **PasswordHistoryService**: Store last login timestamp
3. **UtilityFunction**: Implement absence detection logic
4. **EmailService**: Send notifications for users absent during expiration period

**Implementation Example:**
```csharp
public async Task<DateTime?> GetLastLoginAsync(string userId, string applicationId)
{
    var signIns = await _graphServiceClient.AuditLogs.SignIns
        .GetAsync(requestConfiguration =>
        {
            requestConfiguration.QueryParameters.Filter = 
                $"userId eq '{userId}' and appDisplayName eq '{applicationId}' and status/errorCode eq 0";
            requestConfiguration.QueryParameters.Top = 1;
            requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
        });
    
    return signIns?.Value?.FirstOrDefault()?.CreatedDateTime;
}
```

### **Required Permissions**
- **Delegated**: `AuditLog.Read.All` + `Directory.Read.All`
- **Application**: `AuditLog.Read.All`
- **Role Required**: Security Reader (minimum)

---

## Summary of Native Capabilities vs Custom Implementation

### **Fully Supported by Entra External ID**
1. **Account Lockout**: ✅ Configurable threshold (can set to 5 attempts)
2. **Password Expiration Enforcement**: ✅ `ForceChangePasswordNextSignIn` property
3. **Login Tracking**: ✅ Comprehensive sign-in logs via Graph API

### **Partially Supported**
1. **Lockout Duration**: 🟡 Minimum 60 seconds, progressive lockout (30-minute requirement needs custom logic)

### **Custom Implementation Still Required**
1. **Application-Specific Lockout**: Entra External ID is tenant-wide
2. **30-Minute Fixed Lockout**: Progressive lockout doesn't meet exact requirement
3. **Application-Aware Absence Detection**: Sign-in logs need filtering by application context

---

## Recommended Implementation Strategy

### **Phase 1: Leverage Native Capabilities**
1. **Configure Entra External ID**: Set lockout threshold to 5 attempts
2. **Implement Graph API Integration**: Add sign-in log monitoring
3. **Enable Password Expiration Enforcement**: Use `ForceChangePasswordNextSignIn`

### **Phase 2: Custom Enhancements**
1. **Application-Specific Lockout**: Enhance Azure Functions with application-aware lockout
2. **30-Minute Lockout Logic**: Implement custom lockout duration tracking
3. **Enhanced Absence Detection**: Combine sign-in logs with application context

### **Phase 3: Hybrid Integration**
1. **Unified Lockout Management**: Coordinate Entra External ID and custom lockout
2. **Comprehensive Monitoring**: Dashboard for lockout and absence patterns
3. **Policy Compliance Reporting**: Automated compliance verification

---

## Conclusion

**Entra External ID provides strong native support** for most password policy requirements, particularly account lockout and password expiration enforcement. The **Graph API integration offers excellent capabilities** for login tracking and absence detection.

**Key Success Factors:**
- **Account Lockout**: Native support with configurable thresholds
- **Password Expiration**: Full enforcement via `ForceChangePasswordNextSignIn`
- **Login Tracking**: Rich sign-in logs with comprehensive data
- **Hybrid Approach**: Combine native capabilities with custom Azure Functions logic

**Implementation Readiness:** ✅ **READY** for hybrid implementation leveraging both native Entra External ID capabilities and custom Azure Functions enhancements.
