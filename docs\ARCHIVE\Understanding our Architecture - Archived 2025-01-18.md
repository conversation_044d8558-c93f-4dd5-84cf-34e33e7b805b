# Understanding Our Architecture

---
**ARCHIVED:** January 18, 2025
**Reason:** Consolidated into "PowerPagesCustomAuth Architecture Guide.md"
**Replacement:** See docs/PowerPagesCustomAuth Architecture Guide.md for current architecture documentation
---

**Date**: 2025-01-18
**Purpose**: Document key architectural decisions and design patterns used in PowerPages Custom Authentication
**Audience**: Developers working on this project

## Overview

This document explains the specific architectural choices made in our PowerPages Custom Authentication system, including why certain approaches were selected over alternatives and how they work together to create a secure, maintainable solution.

## 🔐 Authentication Architecture

### Pure MSAL Approach

**Decision**: Use only Microsoft Authentication Library (MSAL) for authentication, avoiding JWT token processing.

**Implementation**:
- **Backend**: `Azure.Identity.ClientSecretCredential` for service-to-service auth
- **Frontend**: `msal.PublicClientApplication` for user authentication
- **No JWT Processing**: Explicitly exclude JWT libraries to prevent mixed approaches

**Why This Choice**:
- **Security**: Single authentication pattern reduces attack surface
- **Consistency**: All auth flows use Microsoft's recommended libraries
- **Maintainability**: One authentication approach to understand and debug
- **Future-Proof**: Aligns with Microsoft's authentication roadmap

### Dependency Exclusion Strategy

**Problem**: Transitive dependencies can pull in unwanted packages.

**Example Without Exclusions**:
```xml
<!-- You only want these packages -->
<PackageReference Include="Microsoft.Graph" Version="5.78.0" />
<PackageReference Include="Azure.Identity" Version="1.14.0" />
```

**Automatic Result** (unwanted):
- ✅ Microsoft.Graph (wanted)
- ✅ Azure.Identity (wanted)  
- ❌ System.IdentityModel.Tokens.Jwt (unwanted - pulled in by Graph)
- ❌ Microsoft.IdentityModel.Tokens (unwanted - pulled in by Graph)
- ❌ Microsoft.IdentityModel.JsonWebTokens (unwanted - pulled in by Graph)

**Our Solution**:
```xml
<!-- Explicitly wanted packages -->
<PackageReference Include="Microsoft.Graph" Version="5.78.0" />
<PackageReference Include="Azure.Identity" Version="1.14.0" />

<!-- Block unwanted transitive dependencies -->
<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.6.1">
  <ExcludeAssets>all</ExcludeAssets>
</PackageReference>
<PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.6.1">
  <ExcludeAssets>all</ExcludeAssets>
</PackageReference>
<PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.6.1">
  <ExcludeAssets>all</ExcludeAssets>
</PackageReference>
```

**How ExcludeAssets Works**:
1. **Downloads** the package (for dependency resolution)
2. **Excludes** it from compilation and runtime
3. **Blocks** transitive inclusion by other packages
4. **Ensures** architectural consistency

**Benefits**:
- Prevents accidental JWT token processing
- Reduces bundle size
- Enforces pure MSAL authentication
- Acts as a "firewall" for dependencies

## 🎯 Token Management Architecture

### Legacy Token Approach

**Decision**: Use separate token managers for different token types instead of unified DataProtection tokens.

**Current Implementation**:

**Password Reset Tokens** (`ResetTokenManager`):
- **Storage**: IMemoryCache (15 minutes)
- **Use Case**: Short-lived, high-frequency operations
- **Benefits**: Fast, simple, automatic cleanup

**Invitation Tokens** (`InvitationTokenManager`):
- **Storage**: Azure Blob Storage (45 days)
- **Use Case**: Long-lived, auditable operations
- **Benefits**: Persistent, full audit trail, supersession tracking

**Why Not DataProtection Tokens**:
- **Complexity**: Current approach is simpler and working
- **Audit Requirements**: Invitation tokens need detailed tracking
- **Performance**: Memory cache is faster for reset tokens
- **Proven**: Legacy approach is battle-tested in production

## 🏗️ Function Architecture

### Separation of Concerns

**Decision**: Split functionality into 4 focused Azure Functions instead of monolithic approach.

**Current Structure**:
- **PasswordFunction**: Password operations with history validation
- **UtilityFunction**: Health checks and administrative operations
- **InvitationFunction**: Invitation token management
- **RegistrationFunction**: User registration with invitation validation

**Benefits**:
- **Scalability**: Each function can scale independently
- **Maintainability**: Clear boundaries and responsibilities
- **Deployment**: Can deploy individual functions without affecting others
- **Monitoring**: Granular telemetry and error tracking

### Service Layer Pattern

**Decision**: Extract business logic into service classes instead of keeping it in function classes.

**Implementation**:
- **Services Directory**: Contains business logic (`PasswordHistoryService`, `EmailService`, etc.)
- **Functions**: Act as thin controllers, handling HTTP concerns only
- **Shared Directory**: Common utilities and models

**Benefits**:
- **Testability**: Services can be unit tested independently
- **Reusability**: Business logic can be shared across functions
- **Separation**: HTTP concerns separated from business logic

## 🔧 Configuration Architecture

### Strongly-Typed Configuration

**Decision**: Use strongly-typed configuration classes instead of accessing configuration directly.

**Implementation**:
```csharp
// Instead of: configuration["SendGrid:ApiKey"]
// Use: IOptions<SendGridOptions> with strongly-typed properties

public class SendGridOptions
{
    public const string SectionName = "SendGrid";
    public string ApiKey { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
}
```

**Benefits**:
- **Type Safety**: Compile-time checking of configuration properties
- **IntelliSense**: IDE support for configuration options
- **Validation**: Can add data annotations for validation
- **Refactoring**: Easier to rename and restructure configuration

## 📦 Dependency Management Philosophy

### Lean Dependencies

**Decision**: Keep only essential packages, remove unused dependencies.

**Process**:
1. **Regular Audits**: Periodically review all package references
2. **Usage Verification**: Confirm each package is actually used in code
3. **Removal**: Remove packages that aren't actively used
4. **Documentation**: Track why each remaining package is necessary

**Recent Cleanup Results**:
- **Removed**: `Microsoft.Identity.Client` (only used in frontend JavaScript)
- **Removed**: `Microsoft.AspNetCore.DataProtection` (unused TokenService implementation)
- **Kept**: All other packages verified as actively used

## 🛡️ Security Architecture

### Defense in Depth

**Layers of Security**:
1. **Function Authorization**: Function-level authorization for all endpoints
2. **Rate Limiting**: Prevent abuse with sliding window rate limiter
3. **Input Validation**: Data annotations and manual validation
4. **Password History**: Prevent password reuse
5. **Token Security**: Cryptographically secure tokens with expiration
6. **Audit Logging**: Comprehensive logging for security monitoring

### Azure Integration

**Decision**: Leverage Azure services for security instead of custom implementations.

**Implementation**:
- **Azure Key Vault**: Sensitive configuration (API keys, secrets)
- **Azure Blob Storage**: Secure token persistence with metadata
- **Azure Identity**: Service authentication
- **Application Insights**: Security monitoring and alerting

## 📋 Development Principles

### YAGNI (You Aren't Gonna Need It)

**Decision**: Implement only what's explicitly required, avoid over-engineering.

**Examples**:
- **Token Management**: Keep working legacy approach instead of migrating to DataProtection
- **Function Count**: 4 focused functions instead of consolidating to fewer
- **Configuration**: Simple strongly-typed options instead of complex configuration frameworks

### Explicit Over Implicit

**Decision**: Make architectural choices explicit and documented.

**Examples**:
- **Dependency Exclusions**: Explicitly exclude JWT packages instead of hoping they won't be included
- **Configuration**: Explicit strongly-typed classes instead of magic strings
- **Error Handling**: Explicit Result<T> pattern instead of exceptions for business logic

## 🔄 Evolution Strategy

### Incremental Improvements

**Approach**: Make small, focused improvements instead of large rewrites.

**Recent Examples**:
- **Rate Limiting**: Replaced custom implementation with modern thread-safe version
- **Dependency Cleanup**: Removed unused packages while preserving functionality
- **CSS Simplification**: Cleaned up variable naming without changing functionality

### Documentation-Driven Changes

**Process**: Document architectural decisions before and after changes.

**Benefits**:
- **Knowledge Transfer**: New developers understand why choices were made
- **Change Tracking**: History of architectural evolution
- **Decision Review**: Can revisit and validate past decisions

---

## 📚 Related Documentation

- **Configuration Reference**: Detailed configuration options and setup
- **Architecture Overview**: High-level system design
- **Security Audit**: Security assessment and recommendations

---

**Last Updated**: 2025-01-18
**Next Review**: When making significant architectural changes
