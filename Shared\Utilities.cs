using Microsoft.Azure.Functions.Worker.Http;
using System.Security.Cryptography;

namespace PasswordHistoryValidator.Shared;


public static class Utilities
{
    // Normalizes email to prevent case sensitivity issues
    public static string NormalizeEmail(string? email)
    {
        return string.IsNullOrWhiteSpace(email) ? string.Empty : email.Trim().ToLowerInvariant();
    }

    // Escapes single quotes for OData query safety
    public static string EscapeODataString(string value)
    {
        return (value ?? string.Empty).Replace("'", "''");
    }


    public static string GenerateVerificationCode()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var randomNumber = BitConverter.ToUInt32(bytes, 0);
        var code = (randomNumber % 900000) + 100000;
        return code.ToString();
    }


    public static string GenerateCorrelationId() => Guid.NewGuid().ToString();

    // Extracts client IP from proxy headers for rate limiting
    public static string GetClientIdentifier(HttpRequestData req)
    {
        var clientIp = req.Headers.GetValues("X-Forwarded-For").FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim()
                      ?? req.Headers.GetValues("X-Real-IP").FirstOrDefault()
                      ?? req.Headers.GetValues("CF-Connecting-IP").FirstOrDefault()
                      ?? "unknown";

        return $"ip:{clientIp}";
    }
}
