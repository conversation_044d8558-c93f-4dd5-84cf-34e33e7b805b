# Configuration Validation Test Guide

## Overview

This guide provides test scenarios to verify the simplified fail-fast configuration validation is working correctly across all Azure Functions services.

## Test Scenarios

### **1. Missing Required Configuration Test**

**Purpose:** Verify application fails at startup with clear error messages when required configuration is missing.

**Test Steps:**
1. **Backup current configuration:**
   ```bash
   cp local.settings.json local.settings.json.backup
   ```

2. **Remove required configuration:**
   ```json
   {
     "Values": {
       "AzureWebJobsStorage": "UseDevelopmentStorage=true",
       "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated"
       // Remove all other configuration
     }
   }
   ```

3. **Start application:**
   ```bash
   func start
   ```

4. **Expected Result:**
   Application should fail to start with error message listing all missing required configuration values.

### **2. Local Development Configuration Test**

**Purpose:** Verify application starts successfully with direct values in local development.

**Test Steps:**
1. **Set direct values in local.settings.json:**
   ```json
   {
     "Values": {
       "AzureWebJobsStorage": "UseDevelopmentStorage=true",
       "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
       "EntraExternalID:TenantId": "your-actual-tenant-id",
       "EntraExternalID:ClientId": "your-actual-client-id",
       "EntraExternalID:ClientSecret": "your-actual-secret",
       "SendGrid:ApiKey": "SG.your-actual-api-key",
       "KeyVaultUrl": ""
     }
   }
   ```

2. **Start application:**
   ```bash
   func start
   ```

3. **Expected Result:**
   Application should start successfully without configuration errors.

### **3. Azure Environment Simulation Test**

**Purpose:** Verify Key Vault requirements are enforced when simulating Azure environment.

**Test Steps:**
1. **Set Azure environment variable:**
   ```json
   {
     "Values": {
       "WEBSITE_SITE_NAME": "test-function-app",
       "EntraExternalID:ClientSecret": "direct-secret-value",
       "SendGrid:ApiKey": "SG.direct-api-key"
     }
   }
   ```

2. **Start application:**
   ```bash
   func start
   ```

3. **Expected Result:**
   Application should fail with errors requiring Key Vault references for sensitive data.

### **4. Optional Configuration Warning Test**

**Purpose:** Verify warnings are logged for optional configuration values outside recommended ranges.

**Test Steps:**
1. **Set extreme values for optional configuration:**
   ```json
   {
     "Values": {
       "PasswordHistory:MaxCount": "100",
       "PasswordHistory:WorkFactor": "5",
       "RateLimit:MaxRequestsPerMinute": "5000"
     }
   }
   ```

2. **Start application and check logs:**
   ```bash
   func start
   ```

3. **Expected Result:**
   Application should start but log warnings about values outside recommended ranges.

### **5. Key Vault URL Format Validation Test**

**Purpose:** Verify Key Vault URL format validation when configured.

**Test Steps:**
1. **Set invalid Key Vault URL:**
   ```json
   {
     "Values": {
       "KeyVaultUrl": "https://invalid-url.com/"
     }
   }
   ```

2. **Start application:**
   ```bash
   func start
   ```

3. **Expected Result:**
   Application should fail with error about invalid Key Vault URL format.

## Email Service Specific Tests

### **6. SendGrid Template ID Validation Test**

**Purpose:** Verify all SendGrid template IDs are validated at startup.

**Test Steps:**
1. **Set missing template IDs:**
   ```json
   {
     "Values": {
       "SendGrid:PasswordResetTemplateId": "",
       "SendGrid:PasswordChangedTemplateId": ""
     }
   }
   ```

2. **Start application:**
   ```bash
   func start
   ```

3. **Expected Result:**
   Application should fail with errors about missing required template IDs.

### **7. Email Service Runtime Test**

**Purpose:** Verify EmailService no longer has null client checks at runtime.

**Test Steps:**
1. **Start application with valid configuration**
2. **Trigger email sending operation** (password reset, registration, etc.)
3. **Check logs for email operations**

**Expected Result:**
- No "SendGrid client not configured" error messages
- Email operations should either succeed or fail with actual SendGrid API errors
- No null reference exceptions

## Production Deployment Tests

### **8. Azure Function App Configuration Test**

**Purpose:** Verify configuration validation works in Azure environment.

**Test Steps:**
1. **Deploy to Azure Function App**
2. **Remove required application setting** (e.g., SendGrid:ApiKey)
3. **Monitor Function App logs**

**Expected Result:**
Function App should fail to start with configuration validation errors in Application Insights logs.

### **9. Key Vault Integration Test**

**Purpose:** Verify Key Vault references work with validation.

**Test Steps:**
1. **Configure Azure Key Vault with secrets**
2. **Set Key Vault references in Function App settings:**
   ```
   SendGrid:ApiKey = @Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/SendGridApiKey/)
   KeyVaultUrl = https://vault.vault.azure.net/
   ```
3. **Deploy and start Function App**

**Expected Result:**
Function App should start successfully and resolve Key Vault references.

## Troubleshooting Common Issues

### **Configuration Not Loading**
- Verify `local.settings.json` is in the correct format
- Check for JSON syntax errors
- Ensure file is not corrupted

### **Key Vault Access Issues**
- Verify managed identity is configured
- Check Key Vault access policies
- Confirm Key Vault URL format is correct

### **Template ID Format Issues**
- SendGrid template IDs should start with "d-"
- Template IDs should be exactly 32 characters after "d-"
- Verify templates exist in SendGrid account

## Expected Error Message Examples

### **Missing Configuration (Local Development):**
```
Configuration validation failed for Local Development environment:

• EntraExternalID:TenantId is required.
• SendGrid:ApiKey is required.

Please configure these values in your application settings, local.settings.json, or Azure Key Vault.
```

### **Azure Environment Key Vault Requirements:**
```
Configuration validation failed for Azure environment:

• EntraExternalID:ClientSecret must use Key Vault reference in Azure environment (@Microsoft.KeyVault).
• SendGrid:ApiKey must use Key Vault reference in Azure environment (@Microsoft.KeyVault).

Please configure these values in your application settings, local.settings.json, or Azure Key Vault.
```

### **Invalid Key Vault URL:**
```
Configuration validation failed for Local Development environment:

• KeyVaultUrl must be a valid Azure Key Vault URL (format: https://your-vault-name.vault.azure.net/).

Please configure these values in your application settings, local.settings.json, or Azure Key Vault.
```

### **Optional Configuration Warnings:**
```
warn: ConfigurationValidation[0]
      PasswordHistory:WorkFactor value 5 is outside recommended range (10-15). Using value anyway.
warn: ConfigurationValidation[0]
      RateLimit:MaxRequestsPerMinute value 5000 is outside recommended range (1-1000). Using value anyway.
```

## Success Criteria

✅ **All required configuration missing:** Application fails to start with comprehensive error message
✅ **Local development:** Application starts successfully with direct configuration values
✅ **Azure environment:** Key Vault references required for sensitive data
✅ **Valid configuration:** Application starts successfully without errors
✅ **Optional warnings:** Warnings logged for values outside recommended ranges
✅ **Key Vault URL validation:** Proper format validation when Key Vault URL is configured
✅ **Runtime reliability:** No configuration-related runtime failures
✅ **Clear error messages:** All error messages provide actionable guidance
