# Placeholder Cleanup Quick Fix Guide

## Immediate Issue

Your application is currently failing to start due to placeholder values in `local.settings.json`. Here's how to fix it immediately:

## Quick Fix (Choose One Option)

### **Option A: Disable Key Vault for Local Development (Recommended)**

Update your `local.settings.json`:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",

    "PasswordHistory:MaxCount": "12",
    "PasswordHistory:WorkFactor": "12",
    "RateLimit:MaxRequestsPerMinute": "60",

    "KeyVaultUrl": "",

    "ApplicationName": "Password Reset Service",

    "EntraExternalID:TenantId": "your-actual-dev-tenant-id",
    "EntraExternalID:ClientId": "your-actual-dev-client-id",
    "EntraExternalID:ClientSecret": "your-actual-dev-secret",
    "EntraExternalID:DefaultDomain": "your-actual-domain.onmicrosoft.com",

    "SendGrid:ApiKey": "SG.your-actual-dev-key",
    "SendGrid:FromEmail": "<EMAIL>",
    "SendGrid:PasswordResetTemplateId": "d-dc912d5057e84d46a0b1e402ededde15",
    "SendGrid:PasswordChangedTemplateId": "d-1ca6c61c4cbf46c7ab74a5086b5af8ac",
    "SendGrid:UserInvitationTemplateId": "d-cb6d9203442b4fb7874b095408af66fc",
    "SendGrid:AccountCreatedTemplateId": "d-b78332752a114a3a98b64a528eb83bcc",
    "SendGrid:PasswordExpirationTemplateId": "d-your-actual-template-id",
    "SendGrid:PasswordExpiredTemplateId": "d-your-actual-template-id",

    "PasswordReset:BaseUrl": "https://auth.tylerlovell.com/reset-password",
    "AccountRegistration:BaseUrl": "https://site-dccs0.powerappsportals.com/Custom-Account-Registration",
    "Invitation:TokenExpirationDays": "45"
  }
}
```

### **Option B: Use Actual Key Vault (If You Have One Set Up)**

```json
{
  "Values": {
    "KeyVaultUrl": "https://your-actual-vault-name.vault.azure.net/",
    "EntraExternalID:ClientSecret": "@Microsoft.KeyVault(SecretUri=https://your-actual-vault-name.vault.azure.net/secrets/EntraClientSecret/)",
    "SendGrid:ApiKey": "@Microsoft.KeyVault(SecretUri=https://your-actual-vault-name.vault.azure.net/secrets/SendGridApiKey/)"
  }
}
```

## Clean Build Outputs

After fixing `local.settings.json`, clean build outputs:

```bash
dotnet clean
dotnet build
```

## Verify Fix

Test that the application starts:

```bash
func start
```

You should see successful startup without configuration validation errors.

## Complete Placeholder Audit

### **Found Placeholders That Need Replacement:**

1. **`REPLACE_WITH_YOUR_ACTUAL_TENANT_ID`** → Your Entra External ID tenant ID
2. **`REPLACE_WITH_YOUR_ACTUAL_CLIENT_ID`** → Your Entra External ID client ID  
3. **`temp-local-dev-secret`** → Your actual development client secret
4. **`yourtenant.onmicrosoft.com`** → Your actual tenant domain
5. **`temp-local-dev-key`** → Your actual SendGrid development API key
6. **`https://your-keyvault-name.vault.azure.net/`** → Your actual Key Vault URL or empty string
7. **`d-placeholder-expiration-template-id`** → Your actual SendGrid template ID
8. **`d-placeholder-expired-template-id`** → Your actual SendGrid template ID

### **Acceptable Values (Keep These):**

✅ **Business Logic Defaults:**
- `"PasswordHistory:MaxCount": "12"`
- `"PasswordHistory:WorkFactor": "12"`
- `"RateLimit:MaxRequestsPerMinute": "60"`
- `"Invitation:TokenExpirationDays": "45"`

✅ **JavaScript Functional Defaults:**
- `const minLength = 8` (password validation)
- `"default-application"` (fallback application ID)
- `"ApplicationNameNotSet"` (fallback application name)

## Prevention for Future

### **1. Create Template File**

Create `local.settings.template.json` for new developers:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    
    "KeyVaultUrl": "",
    
    "EntraExternalID:TenantId": "REPLACE_WITH_YOUR_ACTUAL_TENANT_ID",
    "EntraExternalID:ClientId": "REPLACE_WITH_YOUR_ACTUAL_CLIENT_ID",
    "EntraExternalID:ClientSecret": "REPLACE_WITH_YOUR_ACTUAL_CLIENT_SECRET",
    "EntraExternalID:DefaultDomain": "yourtenant.onmicrosoft.com",
    
    "SendGrid:ApiKey": "REPLACE_WITH_YOUR_SENDGRID_API_KEY"
  }
}
```

### **2. Update .gitignore**

Add to `.gitignore`:
```
local.settings.json
bin/
obj/
```

### **3. Documentation**

Update setup documentation to reference template file and required configuration values.

## Summary

**Immediate Action Required:** Replace placeholder values in `local.settings.json` with actual development configuration values or disable Key Vault by setting `"KeyVaultUrl": ""`.

**Root Cause:** While configuration validation no longer detects specific placeholder patterns, placeholder values will still cause runtime failures when the application attempts to use them.

**Long-term Benefit:** This cleanup ensures your application has working configuration values and proper security practices in place.
