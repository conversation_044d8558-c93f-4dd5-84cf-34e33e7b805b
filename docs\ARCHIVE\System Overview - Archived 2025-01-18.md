
---
**ARCHIVED:** January 18, 2025
**Reason:** Consolidated into "PowerPagesCustomAuth Architecture Guide.md"
**Replacement:** See docs/PowerPagesCustomAuth Architecture Guide.md for current architecture documentation
---

## System Purpose & Architecture

The PowerPagesCustomAuth system is a hybrid authentication solution that combines **standard Entra External ID login** with **custom password history validation** for compliance requirements. It enables multiple Power Pages sites to share a single authentication backend while maintaining complete data isolation between applications.

### Why This Architecture?

- **Compliance Requirements**: Enforces password history rules that standard Entra External ID cannot provide
- **Multi-Application Support**: Single Azure Functions backend serves multiple Power Pages sites with complete data isolation
- **Hybrid Approach**: Leverages Entra External ID for standard authentication while adding custom password management
- **Scalability**: Each component can scale independently based on demand

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Power Pages   │    │ Azure Functions │    │ Entra External  │
│                 │    │                 │    │      ID         │
│ • Custom Forms  │◄──►│ • 3 Services    │◄──►│                 │
│ • JavaScript    │    │ • Business Svc  │    │ • User Storage  │
│ • Standard Auth │    │ • Token Mgmt    │    │ • Authentication│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Azure Blob      │
                       │ Storage         │
                       │ • Password      │
                       │   History       │
                       │ • Tokens        │
                       └─────────────────┘
```

---

## Core Components

### Azure Functions (3 Main Services)

#### **1. PasswordService**
- **Purpose**: All password-related operations with history enforcement
- **Operations**: `validate`, `change`, `reset-request`, `reset-confirm`, `reset-verify`
- **Key Responsibility**: Manages password lifecycle while enforcing history rules

#### **2. AuthenticationService**
- **Purpose**: User registration with invitation validation
- **Operations**: `register`
- **Key Responsibility**: Creates new users in Entra External ID with application context and invitation token management

#### **3. UtilityService**
- **Purpose**: System health monitoring and administrative operations
- **Operations**: `health`, `config-check`
- **Key Responsibility**: Provides operational visibility and system maintenance

### Supporting Services (Internal Business Logic)

#### **PasswordHistoryService**
- **Purpose**: Enforces password history rules with application-specific isolation
- **Key Feature**: Uses scoped user IDs (`{applicationId}/{userId}`) for complete data separation
- **Storage**: Azure Blob Storage with encrypted password hashes

#### **Token Management**
- **ResetTokenManager**: Short-lived password reset tokens (15 minutes, IMemoryCache)
- **InvitationTokenManager**: Long-lived invitation tokens (45 days, Blob Storage)
- **Key Feature**: All tokens include application context for proper isolation

#### **EmailService**
- **Purpose**: Sends transactional emails via SendGrid
- **Templates**: Password reset, account creation, invitation emails
- **Key Feature**: Application-aware email content and branding

---

## Application Separation Model

### Multi-Tenant Architecture Within Single Entra External ID Tenant

The system supports multiple Power Pages applications within a single Entra External ID tenant through:

#### **User Account Isolation**
- **Department Field Strategy**: Uses Entra ID `Department` field to store application context
- **Query Filtering**: All user operations filter by `(email AND department eq 'ApplicationName')`
- **Result**: Same email address can exist in multiple applications without conflicts

#### **Password History Isolation**
- **Scoped Storage**: Password histories stored with application-prefixed keys
- **Implementation**: `GetScopedUserId()` creates `{applicationId}/{userId}` identifiers
- **Result**: Complete separation of password history data between applications

#### **Token Isolation**
- **Application Context**: All tokens (reset, invitation) include `ApplicationId` field
- **Validation**: Token operations require matching application context
- **Result**: Tokens cannot be used across different applications

---

## Key Design Decisions

### Department Field for Application Context
**Decision**: Use Entra ID `Department` field to store application names
**Rationale**: 
- Simple implementation requiring no custom attributes
- Consistent filtering across all Graph API queries
- Immediate availability without additional Entra configuration

### Dual Token Management Strategy
**Decision**: Separate token managers for different use cases
**Rationale**:
- **Reset Tokens**: Short-lived (15 min), high-frequency → IMemoryCache
- **Invitation Tokens**: Long-lived (45 days), audit trail → Blob Storage
- Optimizes performance and storage costs for each use case

### Service Layer Architecture
**Decision**: Extract business logic into dedicated service classes
**Rationale**:
- **Testability**: Services can be unit tested independently
- **Reusability**: Business logic shared across multiple functions
- **Separation**: HTTP concerns separated from business logic

### Function-Level Authorization
**Decision**: All functions require function keys (not anonymous)
**Rationale**:
- **Security**: Prevents unauthorized access to sensitive operations
- **Audit**: All API calls logged with authentication context
- **Control**: Centralized key management through Azure

---

## Integration Points

### Power Pages Configuration Requirements

Each Power Pages site requires these settings:

#### **Core Settings**
- `AzureFunctionUrl`: Base URL for Azure Functions
- `ApplicationName`: Unique identifier for application isolation
- `AzureFunctionKey`: Function key for authenticated access

#### **Authentication Settings**
- `MSALClientId`: Entra External ID client ID
- `MSALTenantId`: Entra External ID tenant ID
- Authentication provider configured for 'openid_2' pattern

### JavaScript Integration Pattern
Power Pages uses meta tags with Liquid templates for configuration:

```html
<meta name="azure-function-url" content="{{ settings['AzureFunctionUrl'] }}">
<meta name="application-name" content="{{ settings['ApplicationName'] }}">
```

JavaScript extracts configuration and makes authenticated API calls to Azure Functions.

---

## Security Model

### Authentication Layers
1. **Function Keys**: All Azure Functions require function keys for access
2. **CORS Protection**: Configured to allow only specific Power Pages domains
3. **Rate Limiting**: Basic protection against brute force attacks
4. **Input Validation**: All inputs validated and sanitized

### Authorization Model
- **Function-Level**: All functions use `AuthorizationLevel.Function`
- **Application-Scoped**: All operations filtered by application context
- **Data Isolation**: Complete separation of user data between applications

### Data Protection
- **Password Hashing**: BCrypt with configurable work factor (default: 12)
- **Secure Storage**: Encrypted blob storage for password history
- **Token Security**: Cryptographically secure tokens with expiration
- **Key Management**: Azure Key Vault for production secrets

---

## Data Flow Examples

### User Registration Flow
1. **Power Pages** → Custom registration form with invitation code
2. **InvitationFunction** → Validates invitation token and application context
3. **RegistrationFunction** → Creates user in Entra External ID with Department field
4. **PasswordHistoryService** → Stores initial password hash with scoped user ID
5. **EmailService** → Sends welcome email

### Password Reset Flow
1. **Power Pages** → Forgot password form submission
2. **PasswordFunction** → Validates email and application context
3. **ResetTokenManager** → Generates secure token and verification code
4. **EmailService** → Sends reset email with verification code
5. **Power Pages** → User enters code and new password
6. **PasswordFunction** → Validates token, updates password, stores history

### Password Change Flow (Authenticated User)
1. **Power Pages** → Password change form
2. **PasswordFunction** → Validates current password and application context
3. **PasswordHistoryService** → Checks new password against history rules
4. **Graph API** → Updates password in Entra External ID
5. **PasswordHistoryService** → Updates password history with scoped user ID
6. **EmailService** → Sends password changed notification

---

## Related Documentation

- **[Application Separation Analysis](./Azure%20Functions%20Authentication%20System%20Application%20Separation%20Analysis%20-%20August%2014th%202024.md)**: Detailed security analysis
- **[API Reference](../docs%20to%20review/PPAuth/API%20Reference.md)**: Complete endpoint documentation
- **[Configuration Reference](../docs%20to%20review/PPAuth/Configuration%20Reference.md)**: Setup and configuration guide
- **[Security Essentials](../docs%20to%20review/PPAuth/Security%20Essentials.md)**: Security configuration requirements

---

## Quick Start for Developers

1. **Understand the Architecture**: Review this document and the application separation analysis
2. **Examine Core Functions**: Start with `AuthenticationFunction.cs` and `PasswordFunction.cs`
3. **Study Service Layer**: Review `PasswordHistoryService.cs` and token managers
4. **Test Integration**: Use `UtilityFunction` health check to verify system connectivity
5. **Review Security**: Understand CORS, function keys, and application isolation mechanisms

This system provides enterprise-grade authentication with strong application separation suitable for production use in multi-application environments.
