# CORS Configuration Enhancement

**Priority:** Testing Phase Priority
**Category:** Security Enhancement
**Impact:** High - Prevents unauthorized cross-origin requests
**Effort:** Low - Configuration update with validation logic

---

## Current Status

**Current Issue:** CORS configuration allows all origins (`*`) in some build configurations, which poses a security risk in production.

**Security Risk:**
- Any website can call Azure Functions
- Potential for CSRF attacks
- Unauthorized access to sensitive operations

**Current Implementation:**
- `host.json` CORS set to allow all origins
- `HttpResponseHelper.CreateCorsResponse()` allows all origins
- No origin validation in place

---

## Implementation Approach

**Single solution addresses both CORS configuration and origin validation:**

1. **Primary Mechanism**: Update `host.json` CORS configuration
2. **Secondary Mechanism**: Server-side origin header validation in `HttpResponseHelper.cs`
3. **Environment-Aware**: Different configurations for development vs. production

---

## Proposed Solution

### **1. Update host.json Configuration**

**Production Configuration:**
```json
{
  "version": "2.0",
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": [
          "https://your-production-site.powerappsportals.com",
          "https://your-staging-site.powerappsportals.com",
          "https://auth.tylerlovell.com"
        ],
        "allowedMethods": ["GET", "POST", "OPTIONS"],
        "allowedHeaders": [
          "Content-Type",
          "Authorization",
          "x-functions-key",
          "X-Requested-With",
          "X-Client-Version"
        ],
        "allowCredentials": false
      }
    }
  }
}
```

**Development Configuration:**
```json
{
  "version": "2.0",
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": ["*"],
        "allowedMethods": ["GET", "POST", "OPTIONS"],
        "allowedHeaders": ["*"]
      }
    }
  }
}
```

### **2. Update HttpResponseHelper.cs**

**Enhanced CORS Response Method:**
```csharp
public static HttpResponseData CreateCorsResponse(HttpRequestData req, IConfiguration? configuration = null)
{
    var response = req.CreateResponse(HttpStatusCode.OK);

    // Get allowed origins from configuration
    var allowedOrigins = GetAllowedOrigins(configuration);
    var origin = req.Headers.GetValues("Origin").FirstOrDefault();

    if (allowedOrigins.Contains("*") || (origin != null && allowedOrigins.Contains(origin)))
    {
        response.Headers.Add("Access-Control-Allow-Origin", origin ?? "*");
    }

    response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization, x-functions-key, X-Requested-With, X-Client-Version");
    response.Headers.Add("Access-Control-Max-Age", "86400");

    return response;
}

private static List<string> GetAllowedOrigins(IConfiguration? configuration)
{
    // Default to allow all for development
    if (configuration == null) return new List<string> { "*" };

    // Get from configuration or environment variable
    var origins = configuration.GetSection("CORS:AllowedOrigins").Get<string[]>();
    return origins?.ToList() ?? new List<string> { "*" };
}
```

### **3. Configuration Management**

**Add to ConfigurationOptions.cs:**
```csharp
public class CorsOptions
{
    public const string SectionName = "CORS";

    public string[] AllowedOrigins { get; set; } = { "*" };
    public string[] AllowedMethods { get; set; } = { "GET", "POST", "OPTIONS" };
    public string[] AllowedHeaders { get; set; } = { "Content-Type", "Authorization", "x-functions-key" };
}
```

---

## Implementation Steps

### **Phase 1: Configuration Setup**
1. Update `host.json` with specific Power Pages domains
2. Add CORS configuration options to `ConfigurationOptions.cs`
3. Update `local.settings.json` with CORS settings

### **Phase 2: Code Updates**
1. Enhance `HttpResponseHelper.CreateCorsResponse()` method
2. Add origin validation logic
3. Update all functions to use enhanced CORS response

### **Phase 3: Testing and Validation**
1. Test with actual Power Pages domains
2. Verify CORS preflight requests work correctly
3. Confirm unauthorized origins are blocked
4. Test development vs. production configurations

---

## Files to Modify

**Configuration Files:**
- `host.json` - Primary CORS configuration
- `local.settings.json` - Development CORS settings
- `Shared/ConfigurationOptions.cs` - Add CorsOptions class

**Code Files:**
- `Shared/HttpResponseHelper.cs` - Enhanced CORS response method
- `Program.cs` - Register CORS configuration options

**Testing:**
- Verify all Power Pages files work with new CORS settings
- Test with browser developer tools
- Validate preflight OPTIONS requests

---

## Security Benefits

**Immediate Security Improvements:**
- Prevents unauthorized cross-origin requests
- Reduces CSRF attack surface
- Limits potential for malicious website abuse

**Production Readiness:**
- Environment-specific CORS configuration
- Proper origin validation
- Compliance with security best practices

---

## Power Pages Domains to Include

**Known Domains (Update with actual values):**
- Production Power Pages site URL
- Staging/Development Power Pages site URL
- Any additional authorized domains

**Configuration Example:**
```json
"CORS:AllowedOrigins": [
  "https://site-dccs0.powerappsportals.com",
  "https://auth.tylerlovell.com",
  "https://your-staging-site.powerappsportals.com"
]
```

---

## Recommendation

**Immediate Action:** Implement CORS configuration with specific Power Pages domains to improve production security.

**Timeline:** Can be implemented in 1-2 hours with immediate security benefits and minimal risk to existing functionality.
