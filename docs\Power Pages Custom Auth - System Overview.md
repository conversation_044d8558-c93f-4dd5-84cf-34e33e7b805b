# PowerPagesCustomAuth System Overview

## Overview
Password management within Power Pages, using Entra External ID for primary authentication. Enforces 12-password history compliance by intercepting standard password operations (registration, reset) with a custom backend built on Azure Functions.

Since password history is not a default feature of Entra External ID, we intercept operations at key points. We create custom Registration and Password Reset scenarios due to limited access with password operations in Entra External ID. Graph API enables us to perform necessary Entra External ID actions while using our Azure Functions for password history.

## Components & Roles

**Power Pages (Frontend)**: Provides the custom UI for registration, password reset, and login. JavaScript handles client-side validation and API calls to the Azure Functions.

**Entra External ID (Identity Provider)**: Handles standard user sign-in and session management via native Power Pages integration (`/.auth/login/EntraExternalID`).

**Azure Functions (Backend Logic)**: Manages all custom password logic through 4 services:
- **PasswordService**: Handles password validation, history enforcement, and password reset operations (all password changes use the reset flow)
- **RegistrationService**: Manages new user registration with invitation validation
- **InvitationService**: Handles user invitation workflow with secure tokens
- **UtilityService**: Provides system health checks, maintenance, and notifications

**Azure Blob Storage (Data Storage)**: Stores the 12-password history for each user in JSON format, with data isolation between Power Pages applications.

**Microsoft Graph API (User Management)**: Used by the Azure Functions to create and update user accounts and passwords in the Entra External ID tenant.

**SendGrid (Email Service)**: Sends emails using dynamic templates for password reset, invitations, and notifications with Osler branding.

## JSON Data Model

### Password History Storage Format
Password history data is stored in Azure Blob Storage as JSON files with the following structure:

```json
{
  "userId": "ApplicationName/<EMAIL>",
  "lastUpdatedUtc": "2025-01-21T10:30:00.000Z",
  "passwordHashes": [
    "$2a$12$hash1...",
    "$2a$12$hash2...",
    "$2a$12$hash3..."
  ]
}
```

**File Structure:**
- **Container**: `passwordhistory`
- **File Name**: `{scopedUserId}.json` (e.g., `MyApp/<EMAIL>`)
- **Content Type**: `application/json`
- **Metadata**: Includes `lastUpdated`, `recordCount`, and `correlationId`

**Field Descriptions:**
- `userId`: Scoped user identifier combining application name and email
- `lastUpdatedUtc`: Timestamp of last password update in UTC
- `passwordHashes`: Array of BCrypt-hashed passwords (most recent first, max 12 entries)

## Microsoft Graph API Integration

### Authentication Configuration
- **Method**: Client Secret Credential (Application permissions)
- **Required Permissions**:
  - `User.ReadWrite.All` - Create and update user accounts
  - `AuditLog.Read.All` - Read sign-in logs for absence detection
  - `Directory.Read.All` - Read directory information

### API Endpoints Used

**User Creation** (`POST /users`):
```json
{
  "displayName": "John Doe (MyApp)",
  "givenName": "John",
  "surname": "Doe",
  "mail": "<EMAIL>",
  "userPrincipalName": "<EMAIL>",
  "department": "MyApp",
  "identities": [
    {
      "signInType": "emailAddress",
      "issuer": "tenant.onmicrosoft.com",
      "issuerAssignedId": "<EMAIL>"
    }
  ],
  "passwordProfile": {
    "password": "userPassword",
    "forceChangePasswordNextSignIn": false
  },
  "accountEnabled": true
}
```

**Password Updates** (`PATCH /users/{userId}`):
```json
{
  "passwordProfile": {
    "password": "newPassword",
    "forceChangePasswordNextSignIn": false
  }
}
```

**User Queries** (`GET /users`):
Filter: `(mail eq 'email' or userPrincipalName eq 'email' or proxyAddresses/any(c:c eq 'SMTP:email')) and department eq 'ApplicationName'`

### Application Isolation Strategy
- **Department Field**: Stores application name for complete user isolation
- **Scoped Queries**: All user operations filter by email AND department
- **Result**: Same email can exist across multiple applications without conflicts

## User Authentication Workflows

### 1. Standard Login
A user navigates to the Power Pages application and is redirected to the standard Entra External ID login page.
Entra External ID validates the user's credentials.
Upon success, the user is redirected back to Power Pages with an authenticated session.

### 2. Custom Registration
A new user fills out the custom registration form on Power Pages using an invitation code.
The form submits the user's details to the RegistrationService Azure Function API.
The Azure Function:
1. Validates the invitation token and verification code.
2. Validates the password complexity.
3. Creates the user in Entra External ID via the Microsoft Graph API.
4. Stores the initial password hash in the Azure Blob Storage history file.

### 3. Password Reset (All Users)
All password changes, whether initiated by logged-in or logged-out users, use the same forgot password flow:
A user (logged-in or logged-out) enters their email into the "Forgot Password" form.
The PasswordService Azure Function triggers SendGrid to send a reset link with verification code.
The user clicks the link and is taken to a custom Power Pages form to enter a new password.
The Azure Function validates the new password against the 12-entry history and updates the user's account via the Graph API.
A confirmation email is sent upon successful reset and the user is redirected to login with their new password.

## Security

**12-Password History Compliance**: Prevents password reuse across all password reset operations. BCrypt hashing is used for secure password storage.

**Application Isolation**: Password history is stored with complete data segregation per Power Pages application using the Department field in Entra External ID.

**Backend Security**: API keys and connection strings are managed in Azure Key Vault with access control.

**Session Invalidation**: User sessions are automatically terminated after a password reset to prevent unauthorized access.

**Production Stability**: Uses Graph API v1.0 for production stability and reliability.

**Authorization**: Azure Functions use function-level authorization with secure function keys for API access protection.

## Notes

**Documentation References**
- Entra External ID
- Power Pages Authentication
- Azure Functions
- Microsoft Graph API

**Current Implementation**:
- 4-Function Architecture: PasswordService, RegistrationService, InvitationService, UtilityService
- Unified Password Change Flow: All users use the same forgot password process
- SendGrid dynamic templates for all email communications
- Azure Key Vault integration for secure configuration
- Production-ready with logging and monitoring
- Azure Functions v4 with resilience patterns
