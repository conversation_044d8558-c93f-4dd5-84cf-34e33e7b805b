using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Threading.RateLimiting;

namespace PasswordHistoryValidator.Shared;


public class RateLimitService : IDisposable
{
    private readonly ILogger<RateLimitService> _logger;
    private readonly PartitionedRateLimiter<string> _rateLimiter;

    public RateLimitService(ILogger<RateLimitService> logger, IOptions<RateLimitOptions> options)
    {
        _logger = logger;
        var rateLimitOptions = options.Value;


        _rateLimiter = PartitionedRateLimiter.Create<string, string>(clientId =>
            RateLimitPartition.GetFixedWindowLimiter(
                partitionKey: clientId,
                factory: _ => new FixedWindowRateLimiterOptions
                {
                    PermitLimit = rateLimitOptions.MaxRequestsPerMinute,
                    Window = TimeSpan.FromMinutes(1),
                    QueueLimit = 0,
                    AutoReplenishment = true
                }));
    }


    public async Task<RateLimitInfo> CheckRateLimitAsync(string clientId, string operation, CancellationToken cancellationToken = default)
    {
        var key = $"{clientId}_{operation}";
        
        using var lease = await _rateLimiter.AcquireAsync(key, 1, cancellationToken);
        
        if (lease.IsAcquired)
        {
            return new RateLimitInfo
            {
                IsAllowed = true,
                WindowResetTime = DateTime.UtcNow.AddMinutes(1)
            };
        }
        else
        {
            _logger.LogWarning("Rate limit exceeded for client {ClientId} operation {Operation}", clientId, operation);

            return new RateLimitInfo
            {
                IsAllowed = false,
                WindowResetTime = lease.TryGetMetadata(MetadataName.RetryAfter, out var retryAfter)
                    ? DateTime.UtcNow.Add(retryAfter)
                    : DateTime.UtcNow.AddMinutes(1)
            };
        }
    }



    public void Dispose()
    {
        _rateLimiter?.Dispose();
        GC.SuppressFinalize(this);
    }
}
